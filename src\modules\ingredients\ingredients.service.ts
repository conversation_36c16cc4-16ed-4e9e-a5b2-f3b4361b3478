import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { NameValidationHelper } from '@/common/helpers/name-validation.helper';
import { MenuItemOption } from '@/modules/menu-item-options/entities/menu-item-option.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { RestaurantAccessService } from '@/modules/shared/restaurant-access/restaurant-access.service';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { CreateIngredientDto } from './dtos/create-ingredient.dto';
import { ListIngredientDto } from './dtos/list-ingredient.dto';
import { UpdateIngredientDto } from './dtos/update-ingredient.dto';
import { Ingredient } from './entities/ingredient.entity';

@Injectable()
export class IngredientsService {
  constructor(
    @InjectRepository(Ingredient)
    private readonly ingredientRepository: Repository<Ingredient>,
    private readonly restaurantAccessService: RestaurantAccessService,
    private readonly dataSource: DataSource,
  ) {}

  async create(createIngredientDto: CreateIngredientDto, ownerId: string | null): Promise<Ingredient> {
    await this.restaurantAccessService.verifyAccessRestaurant(createIngredientDto.restaurantId, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      createIngredientDto.restaurantId,
      createIngredientDto.internalName,
      createIngredientDto.publishedName,
      undefined,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds and menuItemOptionIds from DTO if present
    const { menuItemIds, menuItemOptionIds, ...ingredientData } = createIngredientDto;

    let id: string = '';

    await this.dataSource.transaction(async (entityManager) => {
      // Create ingredient
      const ingredient = entityManager.create(Ingredient, ingredientData);
      const savedIngredient = await entityManager.save(ingredient);

      // Handle menu item relationships if menuItemIds are provided
      await this.handleMenuItemRelationship(savedIngredient, menuItemIds, entityManager);

      // Handle menu item option relationships if menuItemOptionIds are provided
      await this.handleMenuItemOptionRelationship(savedIngredient, menuItemOptionIds, entityManager);

      id = savedIngredient.id;
    });

    return this.findOne(id, ownerId);
  }

  async findAll(listIngredientDto: ListIngredientDto, ownerId: string | null): Promise<Pagination<Ingredient>> {
    const { internalName, publishedName, restaurantId, page, limit } = listIngredientDto;

    const queryBuilder = this.ingredientRepository.createQueryBuilder('ingredient');

    if (restaurantId) {
      queryBuilder.andWhere('ingredient.restaurantId = :restaurantId', { restaurantId });
    }

    // For merchant users, only show ingredients they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('ingredient.restaurant', 'restaurant')
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Apply filters
    if (internalName) {
      queryBuilder.andWhere('ingredient.internalName ILIKE :internalName', { internalName: `%${internalName}%` });
    }

    if (publishedName) {
      queryBuilder.andWhere('ingredient.publishedName ILIKE :publishedName', { publishedName: `%${publishedName}%` });
    }

    // Order by most recently updated
    queryBuilder.orderBy('ingredient.updatedAt', 'DESC');

    const options: IPaginationOptions = { page, limit };

    return paginate<Ingredient>(queryBuilder, options);
  }

  async findOne(id: string, ownerId: string | null): Promise<Ingredient> {
    const queryBuilder = this.ingredientRepository
      .createQueryBuilder('ingredient')
      .where('ingredient.id = :id', { id })
      .leftJoinAndSelect('ingredient.restaurant', 'restaurant')
      .leftJoinAndSelect('ingredient.menuItems', 'menuItems')
      .leftJoinAndSelect('ingredient.menuItemOptions', 'menuItemOptions');

    // For merchant users, only allow access to ingredients they have access to
    if (ownerId) {
      queryBuilder
        .leftJoin('restaurant.brand', 'brand')
        .leftJoin('brand.merchantAccount', 'merchantAccount')
        .andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    const ingredient = await queryBuilder.getOne();

    if (!ingredient) {
      throw new NotFoundException(`Ingredient with ID ${id} not found or you don't have access to it`);
    }

    return ingredient;
  }

  async update(id: string, updateIngredientDto: UpdateIngredientDto, ownerId: string | null): Promise<Ingredient> {
    const ingredient = await this.findOne(id, ownerId);

    // Validate unique names using helper
    await NameValidationHelper.validateUniqueNames(
      ingredient.restaurantId,
      updateIngredientDto.internalName,
      updateIngredientDto.publishedName,
      id,
      this.checkNameExists.bind(this),
    );

    // Extract menuItemIds and menuItemOptionIds from DTO if present
    const { menuItemIds, menuItemOptionIds, ...ingredientData } = updateIngredientDto;

    // Update basic properties
    Object.assign(ingredient, ingredientData);

    await this.dataSource.transaction(async (entityManager) => {
      // Handle menu item relationships if menuItemIds are provided
      await this.handleMenuItemRelationship(ingredient, menuItemIds, entityManager);

      // Handle menu item option relationships if menuItemOptionIds are provided
      await this.handleMenuItemOptionRelationship(ingredient, menuItemOptionIds, entityManager);

      // Save the updated entity
      await entityManager.save(ingredient);
    });

    return this.findOne(id, ownerId);
  }

  /**
   * Helper method to handle ingredient relationships for entities
   * @param entity The entity to update (must have restaurantId and ingredients properties)
   * @param ingredientIds Array of ingredient IDs to link, or undefined to skip updating
   */
  async handleIngredientRelationship<T extends { restaurantId: string; ingredients?: Ingredient[] }>(
    entity: T,
    ingredientIds?: string[],
  ): Promise<void> {
    // If ingredientIds is undefined, don't update ingredients
    if (ingredientIds === undefined) {
      return;
    }

    // If ingredientIds is an empty array, clear all ingredients
    if (ingredientIds.length === 0) {
      entity.ingredients = [];
      return;
    }

    // Find ingredients that match both the provided IDs and the restaurant ID
    const ingredients = await this.ingredientRepository.find({
      where: {
        id: In(ingredientIds),
        restaurantId: entity.restaurantId,
      },
    });

    // Check if all ingredient IDs exist and belong to the restaurant
    if (ingredients.length !== ingredientIds.length) {
      const foundIds = ingredients.map((ingredient) => ingredient.id);
      const missingIds = ingredientIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following ingredient IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Set the ingredients relation
    entity.ingredients = ingredients;
  }

  async checkNameExists(
    restaurantId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      return NameValidationHelper.checkNameExists(
        this.ingredientRepository,
        'ingredient',
        restaurantId,
        'internalName',
        internalName,
        excludeId,
      );
    }

    if (publishedName) {
      return NameValidationHelper.checkNameExists(
        this.ingredientRepository,
        'ingredient',
        restaurantId,
        'publishedName',
        publishedName,
        excludeId,
      );
    }

    return false;
  }

  /**
   * Helper method to handle menu item relationships for ingredients (add ingredient to menu items)
   * @param ingredient The ingredient entity to update
   * @param menuItemIds Array of menu item IDs to link, or undefined to skip updating
   */
  private async handleMenuItemRelationship(
    ingredient: Ingredient,
    menuItemIds: string[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemIds is undefined, don't update menu items
    if (menuItemIds === undefined) {
      return;
    }

    if (!ingredient.id) {
      return;
    }

    // If menuItemIds is an empty array, clear all menu item relationships
    if (menuItemIds.length === 0) {
      ingredient.menuItems = [];
      return;
    }

    // Verify all menu item IDs exist and belong to the same restaurant
    const validMenuItems = await entityManager.find(MenuItem, {
      where: { id: In(menuItemIds), restaurantId: ingredient.restaurantId },
      select: ['id'],
    });

    if (validMenuItems.length !== menuItemIds.length) {
      const foundIds = validMenuItems.map((menuItem) => menuItem.id);
      const missingIds = menuItemIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following menu item IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Set the menu items relation - TypeORM will handle the join table
    ingredient.menuItems = validMenuItems;
  }

  /**
   * Helper method to handle menu item option relationships for ingredients (add ingredient to menu item options)
   * @param ingredient The ingredient entity to update
   * @param menuItemOptionIds Array of menu item option IDs to link, or undefined to skip updating
   */
  private async handleMenuItemOptionRelationship(
    ingredient: Ingredient,
    menuItemOptionIds: string[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    // If menuItemOptionIds is undefined, don't update menu item options
    if (menuItemOptionIds === undefined) {
      return;
    }

    if (!ingredient.id) {
      return;
    }

    // If menuItemOptionIds is an empty array, clear all menu item option relationships
    if (menuItemOptionIds.length === 0) {
      ingredient.menuItemOptions = [];
      return;
    }

    // Verify all menu item option IDs exist and belong to the same restaurant
    const validMenuItemOptions = await entityManager.find(MenuItemOption, {
      where: { id: In(menuItemOptionIds), restaurantId: ingredient.restaurantId },
      select: ['id'],
    });

    if (validMenuItemOptions.length !== menuItemOptionIds.length) {
      const foundIds = validMenuItemOptions.map((option) => option.id);
      const missingIds = menuItemOptionIds.filter((id) => !foundIds.includes(id));
      throw new NotFoundException(
        `The following menu item option IDs do not exist or don't belong to this restaurant: ${missingIds.join(', ')}`,
      );
    }

    // Set the menu item options relation - TypeORM will handle the join table
    ingredient.menuItemOptions = validMenuItemOptions;
  }
}
