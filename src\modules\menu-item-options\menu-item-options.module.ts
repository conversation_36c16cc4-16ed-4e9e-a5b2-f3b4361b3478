import { IngredientsModule } from '@/modules/ingredients/ingredients.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MenuItemOption } from './entities/menu-item-option.entity';
import { MenuItemOptionsController } from './menu-item-options.controller';
import { MenuItemOptionsService } from './menu-item-options.service';

@Module({
  imports: [TypeOrmModule.forFeature([MenuItemOption]), IngredientsModule],
  controllers: [MenuItemOptionsController],
  providers: [MenuItemOptionsService],
  exports: [MenuItemOptionsService],
})
export class MenuItemOptionsModule {}
