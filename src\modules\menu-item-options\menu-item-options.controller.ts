import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuItemOptionDto } from './dtos/create-menu-item-option.dto';
import { ListMenuItemOptionDto } from './dtos/list-menu-item-option.dto';
import { UpdateMenuItemOptionDto } from './dtos/update-menu-item-option.dto';
import { MenuItemOption } from './entities/menu-item-option.entity';
import { MenuItemOptionsService } from './menu-item-options.service';

@ApiTags('Menu Item Options')
@Controller('menu-item-options')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenuItemOptionsController {
  constructor(private readonly menuItemOptionsService: MenuItemOptionsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu item option name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuItemOptionsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(
    @Body() createMenuItemOptionDto: CreateMenuItemOptionDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOption> {
    return this.menuItemOptionsService.create(createMenuItemOptionDto, ownerId);
  }

  @Get()
  findAll(
    @Query() listMenuItemOptionDto: ListMenuItemOptionDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<MenuItemOption>> {
    return this.menuItemOptionsService.findAll(listMenuItemOptionDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<MenuItemOption> {
    return this.menuItemOptionsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuItemOptionDto: UpdateMenuItemOptionDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItemOption> {
    return this.menuItemOptionsService.update(id, updateMenuItemOptionDto, ownerId);
  }
}
