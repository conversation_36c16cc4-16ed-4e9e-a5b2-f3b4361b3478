import { Order } from '@/modules/orders/entities/order.entity';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { ReviewTagsModule } from '../review-tags/review-tags.module';
import { StaffRestaurantReviewsController } from './controllers/staff-restaurant-reviews.controller';
import { UserRestaurantReviewsController } from './controllers/user-restaurant-reviews.controller';
import { RestaurantReviewReply } from './entities/restaurant-review-reply.entity';
import { RestaurantReview } from './entities/restaurant-review.entity';
import { RestaurantReviewsService } from './restaurant-reviews.service';

@Module({
  imports: [TypeOrmModule.forFeature([RestaurantReview, RestaurantReviewReply, Order]), ReviewTagsModule],
  controllers: [UserRestaurantReviewsController, StaffRestaurantReviewsController],
  providers: [RestaurantReviewsService],
  exports: [RestaurantReviewsService],
})
export class RestaurantReviewsModule {}
