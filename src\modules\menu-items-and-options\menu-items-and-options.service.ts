import { isNil } from 'lodash';
import { DataSource } from 'typeorm';

import { MenuItemOptionsService } from '@/modules/menu-item-options/menu-item-options.service';
import { ListAllMenuItemDto } from '@/modules/menu-items/dtos/list-menu-item.dto';
import { BadRequestException, Injectable } from '@nestjs/common';

import { CreateMenuItemOptionDto } from '../menu-item-options/dtos/create-menu-item-option.dto';
import { CreateMenuItemDto } from '../menu-items/dtos/create-menu-item.dto';
import { MenuItemType } from '../menu-items/menu-items.constants';
import { MenuItemsService } from '../menu-items/menu-items.service';
import { CreateUnifiedMenuItemDto } from './dtos/create-unified-menu-item.dto';
import { UpdateUnifiedMenuItemDto } from './dtos/update-unified-menu-item.dto';

@Injectable()
export class MenuItemsAndOptionsService {
  constructor(
    private menuItemsService: MenuItemsService,
    private menuItemOptionsService: MenuItemOptionsService,
    private dataSource: DataSource,
  ) {}

  async findAll(listAllMenuItemDto: ListAllMenuItemDto, ownerId: string | null) {
    const allItems = await Promise.all([
      this.menuItemsService.getAllMenuItems(listAllMenuItemDto, ownerId),
      this.menuItemOptionsService.getAllMenuItemOptions(listAllMenuItemDto, ownerId),
    ]);

    return allItems.flat();
  }

  async checkNameExists(
    restaurantId: string,
    type: MenuItemType,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (type === MenuItemType.ITEM) {
      return this.menuItemsService.checkNameExists(restaurantId, internalName, publishedName, excludeId);
    } else if (type === MenuItemType.OPTION) {
      return this.menuItemOptionsService.checkNameExists(restaurantId, internalName, publishedName, excludeId);
    } else {
      throw new BadRequestException('Invalid type');
    }
  }

  async findOneItemOrOption(id: string, type: MenuItemType, ownerId: string | null) {
    if (type === MenuItemType.ITEM) {
      return this.menuItemsService.findOne(id, ownerId);
    } else if (type === MenuItemType.OPTION) {
      return this.menuItemOptionsService.findOne(id, ownerId);
    } else {
      throw new BadRequestException('Invalid type');
    }
  }

  async deleteItemOrOption(id: string, type: MenuItemType, ownerId: string | null) {
    if (type === MenuItemType.ITEM) {
      return this.menuItemsService.softDelete(id, ownerId);
    } else if (type === MenuItemType.OPTION) {
      return this.menuItemOptionsService.softDelete(id, ownerId);
    } else {
      throw new BadRequestException('Invalid type');
    }
  }

  async createItemOrOption(createUnifiedMenuItemDto: CreateUnifiedMenuItemDto, ownerId: string | null) {
    if (createUnifiedMenuItemDto.type === MenuItemType.ITEM) {
      // Validate required fields for menu item
      if (createUnifiedMenuItemDto.basePrice <= 0) {
        throw new BadRequestException('basePrice must be greater than 0');
      }

      if (!createUnifiedMenuItemDto.imageUrls?.length) {
        throw new BadRequestException('imageUrls is required for menu items');
      }

      const {
        type: _type,
        menuItemOptionGroupsOfOption: _,
        menuItemOptionGroupsOfItem,
        ...rest
      } = createUnifiedMenuItemDto;

      return this.menuItemsService.create(
        {
          ...rest,
          imageUrls: createUnifiedMenuItemDto.imageUrls,
          menuSectionIds: createUnifiedMenuItemDto.menuSectionIds,
          menuItemOptionGroupIds: menuItemOptionGroupsOfItem,
        },
        ownerId,
      );
    } else {
      const {
        type: _type,
        imageUrls: _imageUrls,
        menuItemOptionGroupsOfOption,
        menuItemOptionGroupsOfItem: _,
        menuSectionIds: _menuSectionIds,
        ...rest
      } = createUnifiedMenuItemDto;

      return this.menuItemOptionsService.create(
        {
          ...rest,
          menuItemOptionGroupIds: menuItemOptionGroupsOfOption,
        },
        ownerId,
      );
    }
  }

  async updateItemOrOption(
    id: string,
    ownerId: string | null,
    currentType: MenuItemType,
    updateUnifiedMenuItemDto: UpdateUnifiedMenuItemDto,
  ) {
    const { type: newType, ...data } = updateUnifiedMenuItemDto;

    if (currentType === MenuItemType.ITEM && !isNil(data.basePrice) && data.basePrice <= 0) {
      throw new BadRequestException('basePrice must be greater than 0');
    }

    // If type is being changed, handle conversion
    if (!isNil(newType) && currentType !== newType) {
      return this.handleTypeConversion(id, newType, data, ownerId);
    }

    // Normal update
    if (currentType === MenuItemType.ITEM) {
      const { menuItemOptionGroupsOfOption: _, menuItemOptionGroupsOfItem, ...rest } = data;

      return this.menuItemsService.update(id, { ...rest, menuItemOptionGroupIds: menuItemOptionGroupsOfItem }, ownerId);
    } else {
      const {
        imageUrls: _imageUrls,
        menuItemOptionGroupsOfOption,
        menuItemOptionGroupsOfItem: _,
        menuSectionIds: _menuSectionIds,
        ...rest
      } = data;
      return this.menuItemOptionsService.update(
        id,
        { ...rest, menuItemOptionGroupIds: menuItemOptionGroupsOfOption },
        ownerId,
      );
    }
  }

  private async handleTypeConversion(
    id: string,
    newType: MenuItemType,
    data: UpdateUnifiedMenuItemDto,
    ownerId: string | null,
  ) {
    const { menuItemOptionGroupsOfOption, menuItemOptionGroupsOfItem } = data;
    // Add specific fields based on new type
    if (newType === MenuItemType.ITEM) {
      return await this.dataSource.transaction(async (entityManager) => {
        if (!data.imageUrls?.length) {
          throw new BadRequestException('imageUrls is required when converting to menu item');
        }
        const currentRecord = await this.menuItemOptionsService.softDelete(id, ownerId, entityManager);
        const newRecordData: CreateMenuItemDto = {
          internalName: data.internalName ?? currentRecord.internalName,
          publishedName: data.publishedName ?? currentRecord.publishedName,
          description: data.description ?? currentRecord.description ?? undefined,
          basePrice: data.basePrice ?? currentRecord.basePrice,
          restaurantId: currentRecord.restaurantId,
          scheduleActiveAt: data.scheduleActiveAt ?? currentRecord.scheduleActiveAt?.toISOString() ?? null,
          isActive: data.isActive ?? !!currentRecord.activeAt,
          ingredientIds: data.ingredientIds ?? currentRecord.ingredients?.map((i: any) => i.id),
          imageUrls: data.imageUrls,
          menuItemOptionGroupIds: menuItemOptionGroupsOfItem,
          menuSectionIds: data.menuSectionIds,
        };
        return this.menuItemsService.create(newRecordData, ownerId, entityManager);
      });
    } else {
      return await this.dataSource.transaction(async (entityManager) => {
        const currentRecord = await this.menuItemsService.softDelete(id, ownerId, entityManager);
        const newRecordData: CreateMenuItemOptionDto = {
          internalName: data.internalName ?? currentRecord.internalName,
          publishedName: data.publishedName ?? currentRecord.publishedName,
          description: data.description ?? currentRecord.description ?? undefined,
          basePrice: data.basePrice ?? currentRecord.basePrice,
          restaurantId: currentRecord.restaurantId,
          scheduleActiveAt: data.scheduleActiveAt ?? currentRecord.scheduleActiveAt?.toISOString() ?? null,
          isActive: data.isActive ?? !!currentRecord.activeAt,
          ingredientIds: data.ingredientIds ?? currentRecord.ingredients?.map((i: any) => i.id),
          menuItemOptionGroupIds: menuItemOptionGroupsOfOption,
        };
        return this.menuItemOptionsService.create(newRecordData, ownerId, entityManager);
      });
    }
  }
}
