import { Pagination } from 'nestjs-typeorm-paginate';

import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { CartsService } from '../carts.service';
import { AddToCartDto } from '../dto/add-to-cart.dto';
import { CartQueryDto } from '../dto/cart-query.dto';
import { UpdateCartItemAmountDto } from '../dto/update-cart-item-amount.dto';
import { Cart } from '../entities/cart.entity';

@ApiTags('(User) Carts')
@Controller('user/carts')
@Roles({ userType: UserType.USER, role: '*' })
export class CartsController {
  constructor(private readonly cartsService: CartsService) {}

  @Get()
  findAll(@User('id') userId: string, @Query() query: CartQueryDto): Promise<Pagination<Cart>> {
    return this.cartsService.findAll(userId, query);
  }

  @Post('add')
  addToCart(@User('id') userId: string, @Body() addToCartDto: AddToCartDto): Promise<Cart> {
    return this.cartsService.addToCart(userId, addToCartDto);
  }

  @Get('cart-by-restaurant/:restaurantId')
  findActiveCartByRestaurant(
    @User('id') userId: string,
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
  ): Promise<Cart | undefined> {
    return this.cartsService.findActiveCartByRestaurant(userId, restaurantId);
  }

  @Get('you-might-also-like/:restaurantId')
  getYouMightAlsoLike(
    @User('id') userId: string,
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
  ): Promise<MenuItem[]> {
    return this.cartsService.getYouMightAlsoLike(userId, restaurantId);
  }

  @Put('item/:cartItemId')
  updateCartItemAmount(
    @User('id') userId: string,
    @Param('cartItemId', ParseUUIDPipe) cartItemId: string,
    @Body() updateCartItemAmountDto: UpdateCartItemAmountDto,
  ): Promise<Cart> {
    return this.cartsService.updateCartItemAmount(userId, cartItemId, updateCartItemAmountDto);
  }

  @Delete('item/:cartItemId')
  removeCartItem(@User('id') userId: string, @Param('cartItemId', ParseUUIDPipe) cartItemId: string): Promise<Cart> {
    return this.cartsService.removeCartItem(userId, cartItemId);
  }

  @Delete(':id')
  deleteCart(@User('id') userId: string, @Param('id', ParseUUIDPipe) id: string): Promise<boolean> {
    return this.cartsService.deleteCart(userId, id);
  }
}
