import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { GroupItemWithPriceDto } from '@/common/dtos/group-item-with-price.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMenuItemOptionDto {
  @ApiProperty({ description: 'Internal name of the menu item option' })
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the menu item option' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'Description of the menu item option', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Base price of the menu item option' })
  @IsInt()
  @Min(0)
  @Type(() => Number)
  basePrice: number;

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Array of ingredient IDs', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  ingredientIds?: string[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions and optional price to add this option to',
    required: false,
    type: [GroupItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GroupItemWithPriceDto)
  menuItemOptionGroupIds?: GroupItemWithPriceDto[];
}
