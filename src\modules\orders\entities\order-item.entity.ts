import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';

import { OrderItemOption } from './order-item-option.entity';
import { Order } from './order.entity';

@Entity('order_items')
export class OrderItem extends BaseEntity {
  @Column({ name: 'order_id', type: 'uuid' })
  orderId: string;

  @Column({ name: 'menu_item_id', type: 'uuid' })
  menuItemId: string;

  @Column({ name: 'menu_section_id', type: 'uuid' })
  menuSectionId: string;

  @Column({ type: 'integer' })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  price: number;

  @Column({ name: 'note', nullable: true, type: 'text' })
  note?: string | null;

  @Column({ name: 'menu_section_name', type: 'varchar' })
  menuSectionName: string;

  @Column({ name: 'menu_item_name', type: 'varchar' })
  menuItemName: string;

  @Column({ name: 'menu_item_description', nullable: true, type: 'text' })
  menuItemDescription?: string | null;

  @Column({ name: 'menu_item_image_urls', type: 'json', default: [] })
  menuItemImageUrls: string[];

  @Column({ name: 'modified_at', nullable: true, type: 'timestamptz' })
  modifiedAt?: Date | null;

  @Column({ name: 'modified_by_id', nullable: true, type: 'uuid' })
  modifiedById?: string | null;

  @ManyToOne(() => MerchantStaff)
  @JoinColumn({ name: 'modified_by_id' })
  modifiedBy?: WrapperType<MerchantStaff>;

  @ManyToOne(() => Order, (order) => order.orderItems, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'order_id' })
  order: WrapperType<Order>;

  @OneToMany(() => OrderItemOption, (orderItemOption) => orderItemOption.orderItem)
  orderItemOptions: WrapperType<OrderItemOption>[];
}
