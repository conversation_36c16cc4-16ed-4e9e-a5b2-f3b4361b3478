import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { FilterRestaurantByAddressDto, FilterRestaurantDto } from '../dtos/filter-restaurant.dto';
import { RestaurantSuggestionDto, SuggestionResponseDto } from '../dtos/restaurant-suggestion.dto';
import { Restaurant } from '../entities/restaurant.entity';
import { RestaurantsService } from '../restaurants.service';

@ApiTags('(User) Restaurants')
@Controller('user/restaurants')
@Roles({ userType: UserType.USER, role: '*' })
export class UserRestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Get()
  userFindAll(
    @Query() filterRestaurantDto: FilterRestaurantDto,
    @User('id') userId: string,
  ): Promise<Pagination<Restaurant>> {
    return this.restaurantsService.userFindAll(filterRestaurantDto, userId);
  }

  @Get(':id')
  userFindOne(
    @Param('id', ParseUUIDPipe) id: string,
    @User('id') userId: string,
    @Query() filterRestaurantByAddressDto: FilterRestaurantByAddressDto,
  ): Promise<Restaurant> {
    return this.restaurantsService.userFindOne(id, userId, filterRestaurantByAddressDto.addressId);
  }

  @Post('suggestions')
  async getSuggestions(@Body() dto: RestaurantSuggestionDto): Promise<SuggestionResponseDto> {
    const suggestions = await this.restaurantsService.getSuggestions(dto.search);
    return { suggestions };
  }
}
