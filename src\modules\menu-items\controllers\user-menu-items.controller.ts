import { Public } from '@/modules/auth/decorators/public.decorator';
import { Controller, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { MenuItem } from '../entities/menu-item.entity';
import { MenuItemsService } from '../menu-items.service';

@ApiTags('(User) Menu Items')
@Controller('user/menu-items')
@Public()
export class UserMenuItemsController {
  constructor(private readonly menuItemsService: MenuItemsService) {}

  @Get(':restaurantId/:menuSectionId/:menuItemId')
  userFindOne(
    @Param('restaurantId', ParseUUIDPipe) restaurantId: string,
    @Param('menuSectionId', ParseUUIDPipe) menuSectionId: string,
    @Param('menuItemId', ParseUUIDPipe) menuItemId: string,
  ): Promise<MenuItem> {
    return this.menuItemsService.findOneByUser(restaurantId, menuSectionId, menuItemId);
  }
}
