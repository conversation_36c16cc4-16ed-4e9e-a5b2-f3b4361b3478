import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { UserAddressesModule } from '../user-addresses/user-addresses.module';
import { UsersAdminController } from './controllers/users-admin.controller';
import { UsersController } from './controllers/users.controller';
import { UserChangeLog } from './entities/user-change-log.entity';
import { User } from './entities/user.entity';
import { UserChangeLogService } from './services/user-change-log.service';
import { UsersService } from './users.service';

@Module({
  imports: [TypeOrmModule.forFeature([User, UserChangeLog]), UserAddressesModule],
  controllers: [UsersController, UsersAdminController],
  providers: [UsersService, UserChangeLogService],
  exports: [UsersService, UserChangeLogService],
})
export class UsersModule {}
