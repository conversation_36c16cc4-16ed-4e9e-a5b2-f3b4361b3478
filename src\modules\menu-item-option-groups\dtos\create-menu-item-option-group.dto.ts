import { Type } from 'class-transformer';
import {
  Is<PERSON>rray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { PositionItemWithPriceDto } from '@/common/dtos/position-item-with-price.dto';
import { ApiProperty } from '@nestjs/swagger';

import { MenuItemOptionGroupRule, MenuItemOptionGroupType } from '../entities/menu-item-option-group.entity';

export class CreateMenuItemOptionGroupDto {
  @ApiProperty({ description: 'Internal name of the menu item option group' })
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the menu item option group' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({
    description: 'Rule for the menu item option group',
    enum: MenuItemOptionGroupRule,
    default: MenuItemOptionGroupRule.OPTION,
  })
  @IsNotEmpty()
  @IsEnum(MenuItemOptionGroupRule)
  rule: MenuItemOptionGroupRule;

  @ApiProperty({
    description: 'Type for the menu item option group',
    enum: MenuItemOptionGroupType,
    default: MenuItemOptionGroupType.ITEM_CUSTOMIZATION,
  })
  @IsNotEmpty()
  @IsEnum(MenuItemOptionGroupType)
  type: MenuItemOptionGroupType;

  @ApiProperty({ description: 'Amount of option', required: false, default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxAmountOfOption: number;

  @ApiProperty({ description: 'Maximum amount of options that can be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.OPTION_MAX)
  @IsInt()
  @Min(1)
  @Type(() => Number)
  maxAmount?: number;

  @ApiProperty({ description: 'Fixed amount of options that must be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_FIXED)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  fixedAmount?: number;

  @ApiProperty({ description: 'Minimum amount of options that must be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_RANGE)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  fromAmount?: number;

  @ApiProperty({ description: 'Maximum amount of options that can be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_RANGE)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  toAmount?: number;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({
    description: 'Array of menu item option IDs with positions and optional price',
    required: false,
    type: [PositionItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemWithPriceDto)
  menuItemOptionIds?: PositionItemWithPriceDto[];

  @ApiProperty({ description: 'Array of menu item IDs to add this option group to', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuItemIds?: string[];
}
