import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { PositionItemWithPriceDto } from '@/common/dtos/position-item-with-price.dto';
import { ApiProperty } from '@nestjs/swagger';

import { MenuItemOptionGroupRule, MenuItemOptionGroupType } from '../entities/menu-item-option-group.entity';

export class UpdateMenuItemOptionGroupDto {
  @ApiProperty({ description: 'Internal name of the menu item option group', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the menu item option group', required: false })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  publishedName?: string;

  @ApiProperty({
    description: 'Rule for the menu item option group',
    enum: MenuItemOptionGroupRule,
    required: false,
  })
  @IsOptional()
  @IsEnum(MenuItemOptionGroupRule)
  rule?: MenuItemOptionGroupRule;

  @ApiProperty({ description: 'Type for the menu item option group', enum: MenuItemOptionGroupType })
  @IsOptional()
  @IsEnum(MenuItemOptionGroupType)
  type: MenuItemOptionGroupType;

  @ApiProperty({ description: 'Amount of option', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  maxAmountOfOption: number;

  @ApiProperty({ description: 'Maximum amount of options that can be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.OPTION_MAX)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  maxAmount?: number;

  @ApiProperty({ description: 'Fixed amount of options that must be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_FIXED)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  fixedAmount?: number;

  @ApiProperty({ description: 'Minimum amount of options that must be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_RANGE)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  fromAmount?: number;

  @ApiProperty({ description: 'Maximum amount of options that can be selected', required: false })
  @IsOptional()
  @ValidateIf((o) => o.rule === MenuItemOptionGroupRule.MANDATORY_RANGE)
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  toAmount?: number;

  @ApiProperty({
    description: 'Array of menu item option IDs with positions and optional price',
    required: false,
    type: [PositionItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemWithPriceDto)
  menuItemOptionIds?: PositionItemWithPriceDto[];

  @ApiProperty({ description: 'Array of menu item IDs to add this option group to', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  menuItemIds?: string[];
}
