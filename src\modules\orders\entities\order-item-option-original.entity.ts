import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';

import { OrderItemOriginal } from './order-item-original.entity';

@Entity('order_item_options_original')
@Index(['orderItemOriginalId', 'menuItemOptionGroupId', 'menuItemOptionId'], {
  unique: true,
  where: 'deleted_at IS NULL',
})
export class OrderItemOptionOriginal extends BaseEntity {
  @Column({ name: 'order_item_original_id', type: 'uuid' })
  orderItemOriginalId: string;

  @Column({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @Column({ name: 'menu_item_option_id', type: 'uuid' })
  menuItemOptionId: string;

  @Column({ name: 'amount', default: 1, type: 'integer' })
  amount: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    transformer: new ColumnNumericTransformer(),
  })
  price: number;

  // Duplicate information
  @Column({ name: 'option_group_name', type: 'varchar' })
  optionGroupName: string;

  @Column({ name: 'option_name', type: 'varchar' })
  optionName: string;

  @ManyToOne(() => OrderItemOriginal, (orderItemOriginal) => orderItemOriginal.orderItemOptionsOriginal, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'order_item_original_id' })
  orderItemOriginal: WrapperType<OrderItemOriginal>;
}
