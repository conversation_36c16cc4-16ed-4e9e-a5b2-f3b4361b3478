import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserMerchantStaffJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateRestaurantReviewReplyDto } from '../dto/create-restaurant-review-reply.dto';
import { ListStaffRestaurantReviewDto } from '../dto/list-staff-restaurant-review.dto';
import { RestaurantReviewReply } from '../entities/restaurant-review-reply.entity';
import { RestaurantReview } from '../entities/restaurant-review.entity';
import { RestaurantReviewsService } from '../restaurant-reviews.service';

@ApiTags('(Staff) Restaurant Reviews')
@Controller('staff/restaurants/reviews')
@Roles({ userType: UserType.MERCHANT_STAFF, role: '*' })
export class StaffRestaurantReviewsController {
  constructor(private readonly restaurantReviewsService: RestaurantReviewsService) {}

  @Post('reply')
  @ApiOperation({ summary: 'Reply to a restaurant review' })
  async createRestaurantReviewReplyByStaff(
    @Body() createReplyDto: CreateRestaurantReviewReplyDto,
    @User() user: UserMerchantStaffJwtInfo,
  ): Promise<RestaurantReviewReply> {
    return this.restaurantReviewsService.createRestaurantReviewReplyByStaff(
      user?.restaurantId,
      user?.id,
      createReplyDto,
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get restaurant reviews for staff' })
  async findRestaurantReviewsForStaff(
    @Query() query: ListStaffRestaurantReviewDto,
    @User() user: UserMerchantStaffJwtInfo,
  ): Promise<Pagination<RestaurantReview>> {
    return this.restaurantReviewsService.findRestaurantReviewsForStaff(user?.restaurantId, query);
  }
}
