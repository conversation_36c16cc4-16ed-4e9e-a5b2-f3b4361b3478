import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuItemDto } from '../dtos/create-menu-item.dto';
import { ListMenuItemDto } from '../dtos/list-menu-item.dto';
import { UpdateMenuItemDto } from '../dtos/update-menu-item.dto';
import { MenuItem } from '../entities/menu-item.entity';
import { MenuItemsService } from '../menu-items.service';

@ApiTags('Menu Items')
@Controller('menu-items')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenuItemsController {
  constructor(private readonly menuItemsService: MenuItemsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu item name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuItemsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(@Body() createMenuItemDto: CreateMenuItemDto, @UserMerchantId() ownerId: string | null): Promise<MenuItem> {
    return this.menuItemsService.create(createMenuItemDto, ownerId);
  }

  @Get()
  findAll(
    @Query() listMenuItemDto: ListMenuItemDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<MenuItem>> {
    return this.menuItemsService.findAll(listMenuItemDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<MenuItem> {
    return this.menuItemsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuItemDto: UpdateMenuItemDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuItem> {
    return this.menuItemsService.update(id, updateMenuItemDto, ownerId);
  }
}
