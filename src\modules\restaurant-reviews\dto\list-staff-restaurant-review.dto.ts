import { Transform, Type } from 'class-transformer';
import { IsIn, IsInt, IsOptional, Max, Min } from 'class-validator';

import { PaginationDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class ListStaffRestaurantReviewDto extends PaginationDto {
  @ApiProperty({
    description: 'Filter by rating (1-5)',
    required: false,
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiProperty({
    description: 'Sort field',
    required: false,
    enum: ['createdAt', 'rating'],
    default: 'createdAt',
  })
  @IsOptional()
  @IsIn(['createdAt', 'rating'])
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Sort direction',
    required: false,
    enum: ['ASC', 'DESC'],
    default: 'DESC',
  })
  @IsOptional()
  @Transform(({ value }) => value?.toUpperCase())
  @IsIn(['ASC', 'DESC'])
  sort?: 'DESC';
}
