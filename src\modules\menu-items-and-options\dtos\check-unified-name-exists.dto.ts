import { IsEnum } from 'class-validator';

import { CheckNameExistsDto } from '@/common/dtos/check-name-exists.dto';
import { MenuItemType } from '@/modules/menu-items/menu-items.constants';
import { ApiProperty } from '@nestjs/swagger';

export class CheckUnifiedNameExistsDto extends CheckNameExistsDto {
  @ApiProperty({ description: 'Type of item (item or option)', enum: MenuItemType })
  @IsEnum(MenuItemType)
  type: MenuItemType;
}
