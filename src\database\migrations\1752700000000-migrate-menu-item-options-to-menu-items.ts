import { MigrationInterface, QueryRunner } from 'typeorm';

export class MigrateMenuItemOptionsToMenuItems1752700000000 implements MigrationInterface {
  name = 'MigrateMenuItemOptionsToMenuItems1752700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Add missing columns to menu_items if they don't exist
    // (These columns already exist in menu_items, so we skip this step)

    // Step 2: Migrate data from menu_item_options to menu_items
    await queryRunner.query(`
      INSERT INTO menu_items (
        id, created_at, updated_at, deleted_at, code, internal_name, published_name, 
        description, base_price, type, restaurant_id, schedule_active_at, active_at
      )
      SELECT 
        id, created_at, updated_at, deleted_at, code, internal_name, published_name,
        description, base_price, type, restaurant_id, schedule_active_at, active_at
      FROM menu_item_options
      WHERE NOT EXISTS (
        SELECT 1 FROM menu_items WHERE menu_items.id = menu_item_options.id
      )
    `);

    // Step 3: Create new mapping table for menu_item_option_groups to menu_items
    await queryRunner.query(`
      CREATE TABLE "mapping_menu_item_option_groups_menu_items" (
        "menu_item_option_group_id" uuid NOT NULL,
        "menu_item_id" uuid NOT NULL,
        "position" integer NOT NULL DEFAULT '0',
        "price" numeric(10,2),
        CONSTRAINT "PK_mapping_menu_item_option_groups_menu_items" 
        PRIMARY KEY ("menu_item_option_group_id", "menu_item_id")
      )
    `);

    // Step 4: Migrate mapping data from old table to new table
    await queryRunner.query(`
      INSERT INTO mapping_menu_item_option_groups_menu_items (
        menu_item_option_group_id, menu_item_id, position, price
      )
      SELECT 
        menu_item_option_group_id, menu_item_option_id, position, price
      FROM mapping_menu_item_option_groups_menu_item_options
    `);

    // Step 5: Add foreign key constraints to new mapping table
    await queryRunner.query(`
      ALTER TABLE "mapping_menu_item_option_groups_menu_items" 
      ADD CONSTRAINT "FK_mapping_option_groups_menu_items_group" 
      FOREIGN KEY ("menu_item_option_group_id") 
      REFERENCES "menu_item_option_groups"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "mapping_menu_item_option_groups_menu_items" 
      ADD CONSTRAINT "FK_mapping_option_groups_menu_items_item" 
      FOREIGN KEY ("menu_item_id") 
      REFERENCES "menu_items"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Step 6: Update ingredient mappings from menu_item_options to menu_items
    // First, check if the mapping table exists
    const ingredientMappingExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'mapping_ingredients_menu_item_options'
      )
    `);

    if (ingredientMappingExists[0].exists) {
      // Create new ingredient mapping table for menu_items if it doesn't exist
      const menuItemIngredientMappingExists = await queryRunner.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_name = 'mapping_ingredients_menu_items'
        )
      `);

      if (!menuItemIngredientMappingExists[0].exists) {
        await queryRunner.query(`
          CREATE TABLE "mapping_ingredients_menu_items" (
            "ingredient_id" uuid NOT NULL,
            "menu_item_id" uuid NOT NULL,
            CONSTRAINT "PK_mapping_ingredients_menu_items" 
            PRIMARY KEY ("ingredient_id", "menu_item_id")
          )
        `);

        await queryRunner.query(`
          ALTER TABLE "mapping_ingredients_menu_items" 
          ADD CONSTRAINT "FK_mapping_ingredients_menu_items_ingredient" 
          FOREIGN KEY ("ingredient_id") 
          REFERENCES "ingredients"("id") 
          ON DELETE CASCADE ON UPDATE NO ACTION
        `);

        await queryRunner.query(`
          ALTER TABLE "mapping_ingredients_menu_items" 
          ADD CONSTRAINT "FK_mapping_ingredients_menu_items_menu_item" 
          FOREIGN KEY ("menu_item_id") 
          REFERENCES "menu_items"("id") 
          ON DELETE CASCADE ON UPDATE NO ACTION
        `);
      }

      // Migrate ingredient mappings
      await queryRunner.query(`
        INSERT INTO mapping_ingredients_menu_items (ingredient_id, menu_item_id)
        SELECT ingredient_id, menu_item_option_id
        FROM mapping_ingredients_menu_item_options
        WHERE NOT EXISTS (
          SELECT 1 FROM mapping_ingredients_menu_items 
          WHERE mapping_ingredients_menu_items.ingredient_id = mapping_ingredients_menu_item_options.ingredient_id
          AND mapping_ingredients_menu_items.menu_item_id = mapping_ingredients_menu_item_options.menu_item_option_id
        )
      `);
    }

    // Step 7: Update order_item_options and order_item_options_original tables
    // These tables reference menu_item_option_id, but since we're keeping the same IDs,
    // they should continue to work as the IDs now exist in menu_items table

    // Step 8: Drop old tables and constraints
    await queryRunner.query(`DROP TABLE IF EXISTS "mapping_menu_item_option_groups_menu_item_options"`);
    
    if (ingredientMappingExists[0].exists) {
      await queryRunner.query(`DROP TABLE IF EXISTS "mapping_ingredients_menu_item_options"`);
    }
    
    await queryRunner.query(`DROP TABLE IF EXISTS "menu_item_options"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Step 1: Recreate menu_item_options table
    await queryRunner.query(`
      CREATE TABLE "menu_item_options" (
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "code" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "internal_name" character varying NOT NULL,
        "published_name" character varying NOT NULL,
        "description" character varying,
        "base_price" numeric(10,2) NOT NULL,
        "type" character varying NOT NULL DEFAULT 'option',
        "restaurant_id" uuid NOT NULL,
        "schedule_active_at" TIMESTAMP WITH TIME ZONE,
        "active_at" TIMESTAMP WITH TIME ZONE,
        CONSTRAINT "PK_menu_item_options" PRIMARY KEY ("id")
      )
    `);

    // Step 2: Recreate indexes
    await queryRunner.query(`CREATE UNIQUE INDEX "IDX_menu_item_options_code" ON "menu_item_options" ("code")`);
    await queryRunner.query(`CREATE INDEX "IDX_menu_item_options_restaurant_id" ON "menu_item_options" ("restaurant_id")`);
    await queryRunner.query(`CREATE INDEX "IDX_menu_item_options_schedule_active_at" ON "menu_item_options" ("schedule_active_at")`);
    await queryRunner.query(`CREATE INDEX "IDX_menu_item_options_active_at" ON "menu_item_options" ("active_at")`);

    // Step 3: Migrate data back from menu_items to menu_item_options
    await queryRunner.query(`
      INSERT INTO menu_item_options (
        id, created_at, updated_at, deleted_at, code, internal_name, published_name,
        description, base_price, type, restaurant_id, schedule_active_at, active_at
      )
      SELECT 
        id, created_at, updated_at, deleted_at, code, internal_name, published_name,
        description, base_price, type, restaurant_id, schedule_active_at, active_at
      FROM menu_items
      WHERE type = 'option'
    `);

    // Step 4: Recreate old mapping table
    await queryRunner.query(`
      CREATE TABLE "mapping_menu_item_option_groups_menu_item_options" (
        "menu_item_option_group_id" uuid NOT NULL,
        "menu_item_option_id" uuid NOT NULL,
        "position" integer NOT NULL DEFAULT '0',
        "price" numeric(10,2),
        CONSTRAINT "PK_mapping_menu_item_option_groups_menu_item_options" 
        PRIMARY KEY ("menu_item_option_group_id", "menu_item_option_id")
      )
    `);

    // Step 5: Migrate mapping data back
    await queryRunner.query(`
      INSERT INTO mapping_menu_item_option_groups_menu_item_options (
        menu_item_option_group_id, menu_item_option_id, position, price
      )
      SELECT 
        menu_item_option_group_id, menu_item_id, position, price
      FROM mapping_menu_item_option_groups_menu_items
    `);

    // Step 6: Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" 
      ADD CONSTRAINT "FK_8aef769c00278d1885c97453946" 
      FOREIGN KEY ("menu_item_option_group_id") 
      REFERENCES "menu_item_option_groups"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "mapping_menu_item_option_groups_menu_item_options" 
      ADD CONSTRAINT "FK_5fee1f9aa4a79c2e513c99615b5" 
      FOREIGN KEY ("menu_item_option_id") 
      REFERENCES "menu_item_options"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    // Step 7: Recreate ingredient mapping if needed
    const menuItemIngredientMappingExists = await queryRunner.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'mapping_ingredients_menu_items'
      )
    `);

    if (menuItemIngredientMappingExists[0].exists) {
      await queryRunner.query(`
        CREATE TABLE "mapping_ingredients_menu_item_options" (
          "ingredient_id" uuid NOT NULL,
          "menu_item_option_id" uuid NOT NULL,
          CONSTRAINT "PK_mapping_ingredients_menu_item_options" 
          PRIMARY KEY ("ingredient_id", "menu_item_option_id")
        )
      `);

      await queryRunner.query(`
        INSERT INTO mapping_ingredients_menu_item_options (ingredient_id, menu_item_option_id)
        SELECT ingredient_id, menu_item_id
        FROM mapping_ingredients_menu_items mi
        WHERE EXISTS (
          SELECT 1 FROM menu_item_options mio WHERE mio.id = mi.menu_item_id
        )
      `);

      await queryRunner.query(`DROP TABLE IF EXISTS "mapping_ingredients_menu_items"`);
    }

    // Step 8: Drop new tables
    await queryRunner.query(`DROP TABLE IF EXISTS "mapping_menu_item_option_groups_menu_items"`);

    // Step 9: Remove option items from menu_items
    await queryRunner.query(`DELETE FROM menu_items WHERE type = 'option'`);

    // Step 10: Add foreign key constraints back
    await queryRunner.query(`
      ALTER TABLE "menu_item_options" 
      ADD CONSTRAINT "FK_menu_item_options_restaurant" 
      FOREIGN KEY ("restaurant_id") 
      REFERENCES "restaurants"("id") 
      ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
  }
}
