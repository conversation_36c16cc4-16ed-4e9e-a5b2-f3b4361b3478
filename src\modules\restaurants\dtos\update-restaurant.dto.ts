import { Type } from 'class-transformer';
import {
  <PERSON><PERSON>rray,
  IsBoolean,
  IsDateString,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Max,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

import { ScheduleItem } from './create-restaurant.dto';

export class UpdateRestaurantDto {
  @ApiProperty({ description: 'Internal name of the restaurant', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the restaurant', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Avatar image URL of the restaurant', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_AVATAR)
  avatarImg?: string;

  @ApiProperty({ description: 'Background image URL of the restaurant', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.RESTAURANT_BANNER)
  backgroundImg?: string;

  @ApiProperty({ description: 'Price range of the restaurant', required: false })
  @IsOptional()
  @IsString()
  priceRange?: string;

  @ApiProperty({ description: 'Address of the restaurant', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Ward of the restaurant', required: false })
  @IsOptional()
  @IsString()
  ward?: string;

  @ApiProperty({ description: 'District of the restaurant', required: false })
  @IsOptional()
  @IsString()
  district?: string;

  @ApiProperty({ description: 'Province of the restaurant', required: false })
  @IsOptional()
  @IsString()
  province?: string;

  @ApiProperty({ description: 'Phone number of the restaurant', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({
    description: 'Latitude of the restaurant location',
    required: false,
    example: 10.7769,
    minimum: -90,
    maximum: 90,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Type(() => Number)
  latitude?: number;

  @ApiProperty({
    description: 'Longitude of the restaurant location',
    required: false,
    example: 106.7009,
    minimum: -180,
    maximum: 180,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Type(() => Number)
  longitude?: number;

  @ApiProperty({ description: 'ID of the brand', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;

  @ApiProperty({
    description: 'Available schedule for the restaurant',
    required: false,
    type: [ScheduleItem],
    example: [{ day: 1, start: '10:00', end: '14:00', isAllDay: false }],
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => ScheduleItem)
  availableSchedule?: ScheduleItem[];

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Tags of the restaurant', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  tagIds?: string[];

  @ApiProperty({ description: 'ID of the menu to activate', required: false })
  @IsOptional()
  @IsUUID()
  activeMenuId?: string;
}
