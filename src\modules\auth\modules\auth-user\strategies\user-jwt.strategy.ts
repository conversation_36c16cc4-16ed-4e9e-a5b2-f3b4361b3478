import { Request } from 'express';
import { ExtractJwt, Strategy } from 'passport-jwt';

import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UsersService } from '@/modules/users/users.service';
import { UserJwtInfo, UserJwtPayload } from '@auth/types/jwt-payload.type';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PassportStrategy } from '@nestjs/passport';

@Injectable()
export class UserJwtStrategy extends PassportStrategy(Strategy, 'user-jwt') {
  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        ExtractJwt.fromAuthHeaderAsBearerToken(),
        (request: Request) => {
          const accessToken = request?.cookies?.access_token;
          if (!accessToken) {
            return null;
          }
          return accessToken;
        },
      ]),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.userJwtAccessSecret') as string,
    });
  }

  async validate(payload: UserJwtPayload) {
    const user = await this.usersService.findById(payload.sub);

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    if (user.banned) {
      throw new UnauthorizedException('User is banned');
    }

    const userInfo: UserJwtInfo = {
      id: payload.sub,
      email: user.email,
      userType: UserType.USER,
      firstName: user.firstName,
      lastName: user.lastName,
      hasAddress: payload.hasAddress,
    };

    return userInfo;
  }
}
