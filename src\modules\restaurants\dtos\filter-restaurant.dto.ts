import { Type } from 'class-transformer';
import { <PERSON><PERSON><PERSON>y, IsBoolean, IsIn, IsNumber, IsOptional, IsString, IsUUI<PERSON>, <PERSON>, <PERSON>, MinLength } from 'class-validator';

import { ToArray } from '@/common/decorators/transforms.decorator';
import { PaginationSortDto } from '@/common/dtos/pagination.dto';
import { ApiProperty } from '@nestjs/swagger';

export class FilterRestaurantDto extends PaginationSortDto {
  @ApiProperty({ required: false, default: 'totalOrdersSold', enum: ['starRated', 'totalOrdersSold', 'distance'] })
  @IsOptional()
  @IsString()
  @IsIn(['starRated', 'totalOrdersSold', 'distance'])
  sortBy: string = 'totalOrdersSold';

  @ApiProperty({ description: 'Search by published name and restaurant tags', required: false })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Search text must be at least 3 characters' })
  search?: string;

  @ApiProperty({ description: 'Filter restaurants that are currently open', required: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  openNow?: boolean;

  @ApiProperty({ description: 'Filter by minimum star rating', required: false })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(5)
  @Type(() => Number)
  minStarRated?: number;

  @ApiProperty({ description: 'Filter by brand ID', required: false })
  @IsOptional()
  @IsUUID()
  brandId?: string;

  @ApiProperty({ description: 'Filter by restaurant tags', required: false })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  @ToArray()
  @Type(() => String)
  restaurantTagIds?: string[];

  @ApiProperty({ description: 'Address ID', required: false })
  @IsOptional()
  @IsUUID()
  addressId?: string;
}

export class FilterRestaurantByAddressDto {
  @ApiProperty({ description: 'Address ID', required: false })
  @IsOptional()
  @IsUUID()
  addressId?: string;
}
