import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { SectionItemWithPriceDto } from '@/common/dtos/section-item-with-price.dto';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class CreateMenuItemDto {
  @ApiProperty({ description: 'Internal name of the menu item' })
  @IsString()
  internalName: string;

  @ApiProperty({ description: 'Published name of the menu item' })
  @IsNotEmpty()
  @IsString()
  publishedName: string;

  @ApiProperty({ description: 'Description of the menu item', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Base price of the menu item', required: false })
  @IsInt()
  @IsPositive()
  @Type(() => Number)
  basePrice: number;

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'ID of the restaurant' })
  @IsNotEmpty()
  @IsUUID()
  restaurantId: string;

  @ApiProperty({ description: 'Array of ingredient IDs', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  ingredientIds?: string[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions',
    required: false,
    type: [PositionItemDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemDto)
  menuItemOptionGroupIds?: PositionItemDto[];

  @ApiProperty({
    description: 'Array of menu section IDs',
    required: false,
    type: [SectionItemWithPriceDto],
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionItemWithPriceDto)
  menuSectionIds?: SectionItemWithPriceDto[];

  @ApiProperty({ description: 'Array of menu item image URLs', type: [String] })
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsValidS3Url(FolderType.MENU_ITEMS)
  imageUrls: string[];
}
