import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUUID,
  Min,
  ValidateIf,
  ValidateNested,
} from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { GroupItemWithPriceDto } from '@/common/dtos/group-item-with-price.dto';
import { PositionItemDto } from '@/common/dtos/position-item.dto';
import { SectionItemWithPriceDto } from '@/common/dtos/section-item-with-price.dto';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { MenuItemType } from '@/modules/menu-items/menu-items.constants';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateUnifiedMenuItemDto {
  @ApiProperty({ description: 'New type of item', enum: MenuItemType })
  @IsOptional()
  @IsEnum(MenuItemType)
  type?: MenuItemType;

  @ApiProperty({ description: 'Internal name of the menu item/option', required: false })
  @IsOptional()
  @IsString()
  internalName?: string;

  @ApiProperty({ description: 'Published name of the menu item/option', required: false })
  @IsOptional()
  @IsString()
  publishedName?: string;

  @ApiProperty({ description: 'Description of the menu item/option', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Base price of the menu item/option', required: false })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  basePrice?: number;

  @ApiProperty({ description: 'Schedule active at', required: false, default: new Date().toISOString() })
  @IsOptional()
  @ValidateIf((o) => o.scheduleActiveAt !== null)
  @IsDateString()
  scheduleActiveAt?: string | null;

  @ApiProperty({ description: 'Is active', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  isActive?: boolean;

  @ApiProperty({ description: 'Array of ingredient IDs', required: false, type: [String] })
  @IsOptional()
  @IsArray()
  @IsUUID(undefined, { each: true })
  ingredientIds?: string[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions (only for menu items)',
    required: false,
    type: [GroupItemWithPriceDto],
  })
  @ValidateIf((o) => o.type === MenuItemType.OPTION)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => GroupItemWithPriceDto)
  menuItemOptionGroupsOfOption?: GroupItemWithPriceDto[];

  @ApiProperty({
    description: 'Array of menu item option group IDs with positions (only for menu items)',
    required: false,
    type: [PositionItemDto],
  })
  @ValidateIf((o) => o.type === MenuItemType.ITEM)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PositionItemDto)
  menuItemOptionGroupsOfItem?: PositionItemDto[];

  @ApiProperty({
    description: 'Array of menu section IDs (only for menu items)',
    required: false,
    type: [SectionItemWithPriceDto],
  })
  @ValidateIf((o) => o.type === MenuItemType.ITEM)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionItemWithPriceDto)
  menuSectionIds?: SectionItemWithPriceDto[];

  @ApiProperty({
    description: 'Array of menu item image URLs (required for menu items)',
    type: [String],
    required: false,
  })
  @ValidateIf((o) => o.type === MenuItemType.ITEM)
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @IsValidS3Url(FolderType.MENU_ITEMS)
  imageUrls?: string[];
}
