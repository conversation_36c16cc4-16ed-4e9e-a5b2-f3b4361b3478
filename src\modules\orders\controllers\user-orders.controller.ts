import { Pagination } from 'nestjs-typeorm-paginate';

import { User } from '@/common/decorators/user.decorator';
import { Roles } from '@/modules/auth/decorators/roles.decorator';
import { UserType } from '@/modules/auth/enums/user-type.enum';
import { UserJwtInfo } from '@/modules/auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateOrderDto } from '../dto/create-order.dto';
import { EstimateShippingFeeDto } from '../dto/estimate-shipping-fee.dto';
import { ApproveModificationDto, ConfirmOrderReceivedDto } from '../dto/order-actions.dto';
import { OrderQueryDto } from '../dto/order-query.dto';
import { Order } from '../entities/order.entity';
import { OrdersService } from '../orders.service';

@ApiTags('(User) Orders')
@Controller('user/orders')
@Roles({ userType: UserType.USER, role: '*' })
export class UserOrdersController {
  constructor(private readonly ordersService: OrdersService) {}

  @Post()
  createOrder(@User() user: UserJwtInfo, @Body() createOrderDto: CreateOrderDto): Promise<Order> {
    return this.ordersService.createOrder(user, createOrderDto);
  }

  @Get()
  async findAll(@User('id') userId: string, @Query() query: OrderQueryDto): Promise<Pagination<Order>> {
    return this.ordersService.findAll(query, userId);
  }

  @Get('estimate-shipping-fee')
  async estimateShippingFee(@User('id') userId: string, @Query() query: EstimateShippingFeeDto) {
    return this.ordersService.estimateShippingFee(query, userId);
  }

  @Get('latest-completed')
  @ApiOperation({ summary: 'Get latest completed order and check rating status' })
  async getLatestCompletedOrder(@User('id') userId: string) {
    return this.ordersService.getLatestCompletedOrderWithRatingStatus(userId);
  }

  @Get(':id')
  async findOne(@User('id') userId: string, @Param('id', ParseUUIDPipe) id: string): Promise<Order> {
    return this.ordersService.findOne(id, { userId, relations: true });
  }

  @Put('cancel/:id')
  async cancelOrder(@User('id') userId: string, @Param('id', ParseUUIDPipe) id: string): Promise<boolean> {
    return this.ordersService.cancelOrder(id, userId);
  }

  @Put(':id/mark-seen')
  @ApiOperation({ summary: 'Mark order as seen by user' })
  async markOrderAsSeen(@User('id') userId: string, @Param('id', ParseUUIDPipe) id: string): Promise<Order> {
    return this.ordersService.markOrderAsSeen(id, userId);
  }

  @Put(':id/skip-review')
  @ApiOperation({ summary: 'Skip review for order' })
  async skipReview(@User('id') userId: string, @Param('id', ParseUUIDPipe) id: string): Promise<Order> {
    return this.ordersService.skipReview(id, userId);
  }

  @Post(':id/approve-modification')
  @ApiOperation({ summary: 'Approve or reject order modification' })
  async approveModification(
    @User('id') userId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() approveModificationDto: ApproveModificationDto,
  ): Promise<Order> {
    return this.ordersService.approveModification(id, approveModificationDto, userId);
  }

  @Post(':id/confirm-received')
  @ApiOperation({ summary: 'Confirm order receipt by user' })
  confirmOrderReceived(
    @User('id') userId: string,
    @Param('id', ParseUUIDPipe) id: string,
    @Body() confirmReceiptDto: ConfirmOrderReceivedDto,
  ): Promise<Order> {
    return this.ordersService.confirmOrderReceived(id, confirmReceiptDto, userId);
  }
}
