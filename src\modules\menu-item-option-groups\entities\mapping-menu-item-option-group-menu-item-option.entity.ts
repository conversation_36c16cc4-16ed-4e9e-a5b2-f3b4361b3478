import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';

import { ColumnNumericTransformer } from '@/common/transformers/column-numeric.transformer';
import { MenuItemOption } from '@/modules/menu-item-options/entities/menu-item-option.entity';

import { MenuItemOptionGroup } from './menu-item-option-group.entity';

@Entity('mapping_menu_item_option_groups_menu_item_options')
export class MappingMenuItemOptionGroupMenuItemOption {
  @PrimaryColumn({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @PrimaryColumn({ name: 'menu_item_option_id', type: 'uuid' })
  menuItemOptionId: string;

  @Column({ name: 'position', type: 'int', default: 0 })
  position: number;

  @Column({
    name: 'price',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    transformer: new ColumnNumericTransformer(),
  })
  price?: number | null;

  @ManyToOne(() => MenuItemOptionGroup, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_item_option_group_id' })
  menuItemOptionGroup: WrapperType<MenuItemOptionGroup>;

  @ManyToOne(() => MenuItemOption, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'menu_item_option_id' })
  menuItemOption: WrapperType<MenuItemOption>;
}
