import { MenuItemOptionsModule } from '@/modules/menu-item-options/menu-item-options.module';
import { MenuItemsModule } from '@/modules/menu-items/menu-items.module';
import { Module } from '@nestjs/common';

import { MenuItemsAndOptionsController } from './menu-items-and-options.controller';
import { MenuItemsAndOptionsService } from './menu-items-and-options.service';

@Module({
  imports: [MenuItemsModule, MenuItemOptionsModule],
  controllers: [MenuItemsAndOptionsController],
  providers: [MenuItemsAndOptionsService],
  exports: [MenuItemsAndOptionsService],
})
export class MenuItemsAndOptionsModule {}
