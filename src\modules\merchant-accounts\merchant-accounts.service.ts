import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MerchantUsersService } from '../merchant-users/merchant-users.service';
import { AssignOwnerDto } from './dtos/assign-owner.dto';
import { CreateMerchantAccountDto } from './dtos/create-merchant-account.dto';
import { ListMerchantAccountDto } from './dtos/list-merchant-account.dto';
import { UpdateMerchantAccountDto } from './dtos/update-merchant-account.dto';
import { MerchantAccount } from './entities/merchant-account.entity';

@Injectable()
export class MerchantAccountsService {
  constructor(
    @InjectRepository(MerchantAccount)
    private merchantAccountRepository: Repository<MerchantAccount>,

    private merchantUsersService: MerchantUsersService,
  ) {}

  async create(createMerchantAccountDto: CreateMerchantAccountDto): Promise<MerchantAccount> {
    // Validate ownerMerchantUserId if provided
    if (createMerchantAccountDto.ownerMerchantUserId) {
      await this.validatMerchantUser(createMerchantAccountDto.ownerMerchantUserId);
    }

    const merchantAccount = this.merchantAccountRepository.create(createMerchantAccountDto);
    return this.merchantAccountRepository.save(merchantAccount);
  }

  async findAll(
    listMerchantAccountDto: ListMerchantAccountDto,
    ownerId?: string | null,
  ): Promise<Pagination<MerchantAccount>> {
    const { name, ownerMerchantUserId, page, limit } = listMerchantAccountDto;

    const queryBuilder = this.merchantAccountRepository.createQueryBuilder('merchantAccount');

    // Add relation to ownerMerchantUser but select only specific fields
    queryBuilder
      .leftJoin('merchantAccount.ownerMerchantUser', 'ownerMerchantUser')
      .addSelect(['ownerMerchantUser.id', 'ownerMerchantUser.firstName', 'ownerMerchantUser.lastName']);

    // Filter by owner merchant user ID from request parameter (for user's own accounts)
    if (ownerId) {
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }

    // Filter by owner merchant user ID from query parameter (for admin filtering)
    if (ownerMerchantUserId) {
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerMerchantUserId', { ownerMerchantUserId });
    }

    if (name) {
      queryBuilder.andWhere('merchantAccount.name ILIKE :name', { name: `%${name}%` });
    }

    queryBuilder.orderBy('merchantAccount.updatedAt', 'DESC');

    const options: IPaginationOptions = { page, limit };

    return paginate<MerchantAccount>(queryBuilder, options);
  }

  async findOne(id: string, ownerId?: string | null): Promise<MerchantAccount> {
    const queryOptions: any = {
      where: { id },
      relations: ['ownerMerchantUser', 'brands'],
    };

    // If ownerId is provided, add it to the where condition
    if (ownerId) {
      queryOptions.where.ownerMerchantUserId = ownerId;
    }

    const merchantAccount = await this.merchantAccountRepository.findOne(queryOptions);

    if (!merchantAccount) {
      if (ownerId) {
        throw new NotFoundException(`Merchant account with ID ${id} not found or is not owned by this user`);
      } else {
        throw new NotFoundException(`Merchant account with ID ${id} not found`);
      }
    }

    return merchantAccount;
  }

  async update(
    id: string,
    updateMerchantAccountDto: UpdateMerchantAccountDto,
    ownerId?: string | null,
  ): Promise<MerchantAccount> {
    // Find the merchant account with optional ownerId filter
    const merchantAccount = await this.findOne(id, ownerId);

    Object.assign(merchantAccount, updateMerchantAccountDto);

    return this.merchantAccountRepository.save(merchantAccount);
  }

  async activate(id: string, ownerId?: string | null): Promise<MerchantAccount> {
    const merchantAccount = await this.findOne(id, ownerId);

    merchantAccount.activeAt = new Date();

    return this.merchantAccountRepository.save(merchantAccount);
  }

  async deactivate(id: string, ownerId?: string | null): Promise<MerchantAccount> {
    const merchantAccount = await this.findOne(id, ownerId);

    merchantAccount.activeAt = null;

    return this.merchantAccountRepository.save(merchantAccount);
  }

  async assignOwner({ merchantAccountId, merchantUserId }: AssignOwnerDto): Promise<MerchantAccount> {
    // Find the merchant account
    const merchantAccount = await this.merchantAccountRepository.findOne({
      where: { id: merchantAccountId },
    });

    if (!merchantAccount) {
      throw new NotFoundException(`Merchant account with ID ${merchantAccountId} not found`);
    }

    // Validate ownerMerchantUserId
    await this.validatMerchantUser(merchantUserId);

    // Assign the merchant user as the owner of the merchant account
    merchantAccount.ownerMerchantUserId = merchantUserId;

    return this.merchantAccountRepository.save(merchantAccount);
  }

  async validatMerchantUser(merchantUserId: string) {
    const merchantUser = await this.merchantUsersService.findOne(merchantUserId);
    if (!merchantUser) {
      throw new NotFoundException(`Merchant user with ID ${merchantUserId} not found`);
    }
  }

  async getOne(merchantAccountId: string, ownerId?: string | null) {
    const queryBuilder = this.merchantAccountRepository
      .createQueryBuilder('merchantAccount')
      .select('merchantAccount.id')
      .where('merchantAccount.id = :merchantAccountId', { merchantAccountId });

    if (ownerId) {
      queryBuilder.andWhere('merchantAccount.ownerMerchantUserId = :ownerId', { ownerId });
    }
    return queryBuilder.getOne();
  }
}
