import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MappingMenuItemOptionGroupMenuItem } from './entities/mapping-menu-item-option-group-menu-item.entity';
import { MenuItemOptionGroup } from './entities/menu-item-option-group.entity';
import { MenuItemOptionGroupsController } from './menu-item-option-groups.controller';
import { MenuItemOptionGroupsService } from './menu-item-option-groups.service';

@Module({
  imports: [TypeOrmModule.forFeature([MenuItemOptionGroup, MappingMenuItemOptionGroupMenuItem])],
  controllers: [MenuItemOptionGroupsController],
  providers: [MenuItemOptionGroupsService],
  exports: [MenuItemOptionGroupsService],
})
export class MenuItemOptionGroupsModule {}
