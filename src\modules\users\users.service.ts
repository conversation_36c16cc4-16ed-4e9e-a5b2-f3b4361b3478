import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { Entity<PERSON>anager, Repository } from 'typeorm';

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { UserAddressesService } from '../user-addresses/user-addresses.service';
import { ListUserDto } from './dtos/list-user.dto';
import { ChangeType, UserChangeLog } from './entities/user-change-log.entity';
import { User } from './entities/user.entity';
import { UserChangeLogService } from './services/user-change-log.service';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    private userAddressesService: UserAddressesService,
    private userChangeLogService: UserChangeLogService,
  ) {}

  async updateUserPhone(
    oldUser: User,
    userData: { email: string; phone: string; phoneCountryCode: string },
    manager: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ) {
    const otherUser = await this.removePhoneFromAnyOtherUser(userData, manager);

    if (otherUser) await this.createUserChangeLog(otherUser, oldUser.id, manager);

    await manager.update(
      User,
      { email: userData.email },
      { phone: userData.phone, phoneCountryCode: userData.phoneCountryCode },
    );

    const updatedUser = (await manager.findOne(User, { where: { email: userData.email } })) as User;

    // Log the phone change
    if (oldUser.phone !== userData.phone || oldUser.phoneCountryCode !== userData.phoneCountryCode) {
      await this.userChangeLogService.createChangeLog({
        userId: updatedUser.id,
        changeType: ChangeType.PHONE,
        beforeData: {
          phone: oldUser.phone,
          phoneCountryCode: oldUser.phoneCountryCode,
        },
        afterData: {
          phone: userData.phone,
          phoneCountryCode: userData.phoneCountryCode,
        },
        changedByUserId: updatedUser.id,
      });
    }

    return updatedUser;
  }

  async createNewUser(
    userData: { email: string; phone: string; phoneCountryCode: string },
    manager: EntityManager,
  ): Promise<User> {
    const otherUser = await this.removePhoneFromAnyOtherUser(userData, manager);
    const user = manager.create(User, userData);
    const savedUser = await manager.save(user);
    if (otherUser) await this.createUserChangeLog(otherUser, savedUser.id, manager);
    return savedUser;
  }

  async removePhoneFromAnyOtherUser(
    userData: { email: string; phone: string; phoneCountryCode: string },
    manager: EntityManager,
  ) {
    const { phone, phoneCountryCode } = userData;

    const otherUser = await manager.findOne(User, { where: { phone, phoneCountryCode } });

    if (otherUser) {
      await manager.update(User, otherUser.id, { phone: null, phoneCountryCode: null });
    }
    return otherUser;
  }

  private async createUserChangeLog(userUpdated: User, changedByUserId: string, manager: EntityManager) {
    await manager.save(UserChangeLog, {
      userId: userUpdated.id,
      changeType: ChangeType.PHONE,
      beforeData: {
        phone: userUpdated.phone,
        phoneCountryCode: userUpdated.phoneCountryCode,
      },
      afterData: {
        phone: null,
        phoneCountryCode: null,
      },
      changedByUserId: changedByUserId,
    });
  }

  async getMe(id: string) {
    const user = await this.usersRepository.findOne({ where: { id } });

    if (!user) {
      throw new NotFoundException(`User not found `);
    }
    const defaultAddress = await this.userAddressesService.getDefaultAddress(id);
    return {
      ...user,
      hasAddress: !!defaultAddress,
      defaultAddress,
    };
  }

  async findAll(listUserDto: ListUserDto): Promise<Pagination<User>> {
    const { page, limit } = listUserDto;
    const options: IPaginationOptions = { page, limit };
    return paginate<User>(this.usersRepository, options, {
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  async findById(id: string): Promise<User> {
    const user = await this.usersRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async findOneByEmail(email: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { email } });
  }

  async findOneByPhone(phone: string): Promise<User | null> {
    return this.usersRepository.findOne({ where: { phone } });
  }

  async updateProfile(id: string, updateData: Partial<User>): Promise<User> {
    const oldUser = await this.findById(id);

    // Log changes for firstName and lastName
    const beforeData: Record<string, any> = {};
    let isChanged = false;
    const afterData: Record<string, any> = {};
    for (const field of ['firstName', 'lastName'] as const) {
      if (updateData[field] && updateData[field] !== oldUser[field]) {
        beforeData[field] = oldUser[field];
        afterData[field] = updateData[field];
        isChanged = true;
      }
    }
    if (isChanged) {
      await this.userChangeLogService.createChangeLog({
        userId: id,
        changeType: ChangeType.OTHER_INFO,
        beforeData,
        afterData,
        changedByUserId: id,
      });

      await this.usersRepository.update(id, updateData);
    }
    return this.findById(id);
  }

  async updateUserEmail(
    oldUser: User,
    newEmail: string,
    changeLogData?: { userAgent?: string; ipAddress?: string },
  ): Promise<User> {
    const userId = oldUser.id;
    // Log the change
    await this.userChangeLogService.createChangeLog({
      userId,
      changeType: ChangeType.EMAIL,
      beforeData: { email: oldUser.email },
      afterData: { email: newEmail },
      changedByUserId: userId,
      userAgent: changeLogData?.userAgent,
      ipAddress: changeLogData?.ipAddress,
    });

    await this.usersRepository.update(userId, { email: newEmail });
    return this.findById(userId);
  }

  async updateUserPhoneNumber(
    oldUser: User,
    newPhone: string,
    newPhoneCountryCode: string,
    changeLogData?: { userAgent?: string; ipAddress?: string },
  ): Promise<User> {
    const userId = oldUser.id;

    // Remove phone from any other user first (existing logic)
    await this.usersRepository.update(
      { phone: newPhone, phoneCountryCode: newPhoneCountryCode },
      { phone: null, phoneCountryCode: null },
    );

    // Log the change
    await this.userChangeLogService.createChangeLog({
      userId,
      changeType: ChangeType.PHONE,
      beforeData: {
        phone: oldUser.phone,
        phoneCountryCode: oldUser.phoneCountryCode,
      },
      afterData: {
        phone: newPhone,
        phoneCountryCode: newPhoneCountryCode,
      },
      changedByUserId: userId,
      userAgent: changeLogData?.userAgent,
      ipAddress: changeLogData?.ipAddress,
    });

    await this.usersRepository.update(userId, {
      phone: newPhone,
      phoneCountryCode: newPhoneCountryCode,
    });
    return this.findById(userId);
  }

  async ban(id: string) {
    const user = await this.findById(id);
    user.banned = true;
    return this.usersRepository.save(user);
  }

  async unban(id: string) {
    const user = await this.findById(id);
    user.banned = false;
    return this.usersRepository.save(user);
  }
}
