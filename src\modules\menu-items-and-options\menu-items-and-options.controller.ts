import { UserMerchantId } from '@/common/decorators/user.decorator';
import { NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiQuery, ApiTags } from '@nestjs/swagger';

import { ListAllMenuItemDto } from '../menu-items/dtos/list-menu-item.dto';
import { MenuItemType } from '../menu-items/menu-items.constants';
import { CheckUnifiedNameExistsDto } from './dtos/check-unified-name-exists.dto';
import { CreateUnifiedMenuItemDto } from './dtos/create-unified-menu-item.dto';
import { QueryUnifiedMenuItemDto } from './dtos/query-unified-menu-item.dto';
import { UpdateUnifiedMenuItemDto } from './dtos/update-unified-menu-item.dto';
import { MenuItemsAndOptionsService } from './menu-items-and-options.service';

@ApiTags('Menu Items and Options')
@Controller('menu-items-and-options')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenuItemsAndOptionsController {
  constructor(private readonly menuItemsAndOptionsService: MenuItemsAndOptionsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if name exists for menu item or option' })
  async checkExists(@Body() dto: CheckUnifiedNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuItemsAndOptionsService.checkNameExists(
      dto.restaurantId,
      dto.type,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Get()
  @ApiOperation({ summary: 'Get all menu items and menu item options' })
  findAll(@Query() listAllMenuItemDto: ListAllMenuItemDto, @UserMerchantId() ownerId: string | null) {
    return this.menuItemsAndOptionsService.findAll(listAllMenuItemDto, ownerId);
  }

  @Post()
  @ApiOperation({ summary: 'Create menu item or menu item option based on type' })
  create(@Body() createUnifiedMenuItemDto: CreateUnifiedMenuItemDto, @UserMerchantId() ownerId: string | null) {
    return this.menuItemsAndOptionsService.createItemOrOption(createUnifiedMenuItemDto, ownerId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get menu item or menu item option by ID and type' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryUnifiedMenuItemDto: QueryUnifiedMenuItemDto,
    @UserMerchantId() ownerId: string | null,
  ) {
    return this.menuItemsAndOptionsService.findOneItemOrOption(id, queryUnifiedMenuItemDto.type, ownerId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update menu item or menu item option with type conversion support' })
  @ApiParam({ name: 'id', description: 'ID of the item', type: 'string' })
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryUnifiedMenuItemDto: QueryUnifiedMenuItemDto,
    @Body() updateUnifiedMenuItemDto: UpdateUnifiedMenuItemDto,
    @UserMerchantId() ownerId: string | null,
  ) {
    return this.menuItemsAndOptionsService.updateItemOrOption(
      id,
      ownerId,
      queryUnifiedMenuItemDto.type,
      updateUnifiedMenuItemDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete menu item or menu item option by ID and type' })
  @ApiQuery({ name: 'type', description: 'Type of item (item or option)', enum: MenuItemType })
  async delete(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() queryUnifiedMenuItemDto: QueryUnifiedMenuItemDto,
    @UserMerchantId() ownerId: string | null,
  ) {
    return this.menuItemsAndOptionsService.deleteItemOrOption(id, queryUnifiedMenuItemDto.type, ownerId);
  }
}
