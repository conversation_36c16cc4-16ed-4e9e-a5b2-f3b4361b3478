<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Geofencing Test Tool</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" />

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />

    <!-- Leaflet Draw CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.css" />

    <style>
      #map {
        height: 600px;
        border: 2px solid #ddd;
        border-radius: 8px;
      }

      .login-panel {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 15px;
        font-size: 14px;
      }

      .login-panel .form-control {
        font-size: 13px;
        padding: 4px 8px;
      }

      .login-panel .btn {
        font-size: 13px;
        padding: 4px 12px;
      }

      .login-panel h6 {
        margin-bottom: 8px;
        font-size: 14px;
      }

      .login-panel .mb-3 {
        margin-bottom: 8px !important;
      }

      .login-panel .form-label {
        font-size: 12px;
        margin-bottom: 3px;
        font-weight: 500;
      }

      .login-panel .form-check-label {
        font-size: 12px;
      }

      .login-panel p {
        margin-bottom: 5px;
        font-size: 13px;
      }

      .compact-section {
        margin-bottom: 15px;
      }

      .login-panel.collapsed {
        padding: 8px 10px;
      }

      .login-panel.collapsed h6 {
        margin-bottom: 0;
        cursor: pointer;
      }

      .status-indicator.clickable {
        cursor: pointer;
      }

      .login-panel #loginFormContainer {
        transition: max-height 0.3s ease;
        overflow: hidden;
        max-height: 300px; /* Default expanded height */
      }

      .login-panel #loginFormContainer.collapsed {
        max-height: 0;
        padding-top: 0;
        padding-bottom: 0;
      }

      .login-panel .toggle-arrow {
        transition: transform 0.3s ease;
      }

      .login-panel .toggle-arrow.rotated {
        transform: rotate(180deg);
      }

      .geofencing-item {
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 10px;
        background: #fff;
      }

      .geofencing-item.active {
        border-color: #0d6efd;
        background: #e7f3ff;
      }

      .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
      }

      .status-logged-in {
        background-color: #28a745;
      }
      .status-logged-out {
        background-color: #dc3545;
      }

      .drawing-controls {
        background: white;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .coordinate-display {
        font-family: monospace;
        background: #f8f9fa;
        padding: 2px 6px;
        border-radius: 3px;
        border: 1px solid #dee2e6;
      }

      #mouseCoords,
      #myLocationCoords {
        font-family: monospace;
        font-weight: 500;
      }

      .temp-drawing-item {
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 5px;
        background: #fff;
        font-size: 12px;
      }

      .temp-drawing-item.selected {
        border-color: #0d6efd;
        background: #e7f3ff;
      }

      .temp-drawing-controls {
        display: flex;
        gap: 5px;
        margin-top: 5px;
      }

      .temp-drawing-type {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        color: white;
      }

      .type-polygon {
        background-color: #0d6efd;
      }
      .type-circle {
        background-color: #198754;
      }
      .type-rectangle {
        background-color: #fd7e14;
      }

      @media (max-width: 768px) {
        .col-md-4 {
          margin-bottom: 20px;
        }

        .login-panel {
          font-size: 13px;
        }
      }
      .login-panel .form-control:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }

      #restaurantSelect:focus {
        border-color: #198754;
        box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
      }

      .alert-sm {
        padding: 6px 10px;
        font-size: 12px;
        line-height: 1.3;
      }

      .geofencing-status {
        max-height: 150px;
        overflow-y: auto;
      }

      #locationInfo {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px;
      }

      #locationBtn {
        position: relative;
        transition: all 0.3s ease;
      }

      #locationBtn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- Control Panel -->
        <div class="col-md-4">
          <h2>Geofencing Test Tool</h2>

          <!-- Restaurant Selection -->
          <div class="compact-section">
            <label class="form-label">Select Restaurant</label>
            <select class="form-select" id="restaurantSelect" onchange="loadRestaurantGeofencing()">
              <option value="">🏪 Select a restaurant to start...</option>
            </select>
            <button class="btn btn-secondary btn-sm mt-2" onclick="loadRestaurants()">Reload Restaurants</button>
          </div>

          <!-- Drawing Controls -->
          <div class="drawing-controls compact-section">
            <h6>Drawing Tools</h6>
            <!-- Temporary Drawings -->
            <div id="tempDrawingsSection" style="display: none">
              <h6>Temporary Drawings (<span id="tempDrawingCount">0</span>)</h6>
              <div id="tempDrawingsList" class="mb-2" style="max-height: 200px; overflow-y: auto"></div>
              <div class="mb-2">
                <label class="form-label">Default Shipping Fee (VND)</label>
                <input
                  type="number"
                  class="form-control form-control-sm"
                  id="defaultShippingFee"
                  value="15000"
                  min="0"
                />
              </div>
              <button class="btn btn-success w-100" onclick="saveAllTempDrawings()">Save All Drawings</button>
              <button class="btn btn-outline-warning w-100 mt-1" onclick="clearTempDrawings()">
                Clear Temp Drawings
              </button>
            </div>
          </div>

          <!-- My Location -->
          <div class="compact-section">
            <h6>📍 My Location</h6>
            <div class="mb-2">
              <button class="btn btn-info w-100" onclick="getMyLocation()" id="locationBtn">
                <span id="locationBtnText">Get My Location</span>
              </button>
            </div>
            <div class="form-check mb-2">
              <input type="checkbox" class="form-check-input" id="autoLocation" />
              <label class="form-check-label" for="autoLocation">Auto-get location on load</label>
            </div>
            <div id="locationInfo" class="small text-muted" style="display: none">
              <div><strong>Coordinates:</strong></div>
              <div>Lat: <span id="currentLat">-</span></div>
              <div>Lng: <span id="currentLng">-</span></div>
              <div>Accuracy: <span id="currentAccuracy">-</span>m</div>
              <div class="mt-1">
                <button class="btn btn-outline-secondary btn-sm" onclick="centerMapOnMyLocation()">Center Map</button>
              </div>
            </div>
          </div>

          <!-- Current Geofencing List -->
          <div class="compact-section">
            <h6>Current Geofencing Areas (<span id="geofencingCount">0</span>)</h6>
            <div class="mb-2">
              <button class="btn btn-outline-secondary btn-sm me-1" onclick="exportGeofencing()">Export</button>
              <button class="btn btn-outline-secondary btn-sm me-1" onclick="importGeofencing()">Import</button>
              <button class="btn btn-outline-warning btn-sm" onclick="clearAllGeofencing()">Clear All</button>
            </div>
            <div id="geofencingList"></div>
            <button class="btn btn-primary w-100 mt-2" onclick="submitAllGeofencing()" disabled id="submitBtn">
              Update All Geofencing
            </button>
          </div>

          <!-- Login Panel -->
          <div class="login-panel compact-section">
            <h6 onclick="toggleLoginPanel()" style="cursor: pointer">
              <span id="loginStatus" class="status-indicator status-logged-out"></span>
              Authentication
              <small class="float-end toggle-arrow">⯆</small>
            </h6>
            <div id="loginFormContainer">
              <div id="loginForm">
                <div class="mb-3">
                  <label class="form-label">API Base URL</label>
                  <input
                    type="text"
                    class="form-control"
                    id="apiBaseUrl"
                    value="http://localhost:3000"
                    placeholder="http://localhost:3000"
                  />
                </div>
                <div class="mb-3">
                  <label class="form-label">Phone/Email</label>
                  <input type="text" class="form-control" id="loginPhone" placeholder="Enter phone number or email" />
                </div>
                <div class="mb-3">
                  <label class="form-label">Password</label>
                  <input type="password" class="form-control" id="loginPassword" placeholder="Enter your password" />
                </div>
                <div class="mb-3 form-check">
                  <input type="checkbox" class="form-check-input" id="rememberMe" checked />
                  <label class="form-check-label" for="rememberMe">Remember login info</label>
                </div>
                <button class="btn btn-primary" onclick="login()">Login</button>
                <button class="btn btn-outline-secondary ms-2" onclick="clearSavedData()">Clear Saved</button>
              </div>
              <div id="loggedInInfo" style="display: none">
                <p>Logged in as: <span id="userInfo"></span></p>
                <button class="btn btn-outline-danger btn-sm" onclick="logout()">Logout</button>
              </div>
            </div>
          </div>

          <!-- API Response -->
          <div class="compact-section">
            <h6>API Response</h6>
            <textarea id="apiResponse" class="form-control" rows="4" readonly></textarea>
          </div>
        </div>

        <!-- Map Container -->
        <div class="col-md-8">
          <div id="map"></div>
          <div class="mt-2 small text-muted">
            <strong>Mouse:</strong> <span id="mouseCoords">Move mouse over map</span>
            <span id="currentLocationDisplay" style="display: none">
              | <strong>My Location:</strong> <span id="myLocationCoords">-</span></span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- Toast Container -->
    <div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999"></div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

    <!-- Leaflet Draw JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet.draw/1.0.4/leaflet.draw.js"></script>

    <script>
      // Global variables
      let map;
      let drawControl;
      let drawnItems;
      let currentGeofencing = [];
      let tempDrawings = []; // For multiple drawings before save
      let editingTempDrawingIndex = null; // Chỉ số mục đang sửa
      let authToken = null;
      let apiBaseUrl = 'http://localhost:3000';
      let currentRestaurantId = null;
      let tempLayer = null;
      let drawingCounter = 1;
      let currentLocationMarker = null;

      // LocalStorage keys
      const STORAGE_KEYS = {
        API_BASE_URL: 'geofencing_api_base_url',
        LOGIN_PHONE: 'geofencing_login_phone',
        LOGIN_PASSWORD: 'geofencing_login_password',
        REMEMBER_ME: 'geofencing_remember_me',
      };

      // Initialize map
      function initMap() {
        // Initialize map centered on Ho Chi Minh City
        map = L.map('map').setView([10.7769, 106.7009], 13);

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: ' OpenStreetMap contributors',
        }).addTo(map);

        // Initialize drawn items layer
        drawnItems = new L.FeatureGroup();
        map.addLayer(drawnItems);

        // Luôn hiển thị Leaflet Draw Toolbar
        drawControl = new L.Control.Draw({
          draw: {
            polygon: {
              allowIntersection: false,
              showArea: true,
              drawError: {
                color: '#e1e100',
                message: '<strong>Error:</strong> Polygon edges cannot cross!',
              },
            },
            circle: {
              showRadius: true,
              metric: true,
            },
            rectangle: {},
            polyline: false,
            marker: false,
            circlemarker: false,
          },
          edit: false,
        });
        map.addControl(drawControl);

        // Sự kiện vẽ xong
        map.on('draw:created', function (e) {
          const layer = e.layer;
          // Generate random color for this drawing
          const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff', '#ffa500', '#800080'];
          const randomColor = colors[Math.floor(Math.random() * colors.length)];
          // Style the layer
          layer.setStyle({
            fillColor: randomColor,
            fillOpacity: 0.3,
            color: randomColor,
            weight: 2,
          });
          // Add to map
          drawnItems.addLayer(layer);
          // Convert to geometry data
          const geometryData = layerToGeometryData(layer, randomColor);
          // Create temp drawing object
          const currentDefaultFee = parseFloat(document.getElementById('defaultShippingFee').value) || 15000;
          const tempDrawing = {
            id: `temp_${Date.now()}_${drawingCounter}`,
            name: `${geometryData.type.charAt(0).toUpperCase() + geometryData.type.slice(1)} Area ${drawingCounter}`,
            description: '',
            type: geometryData.type.toUpperCase(),
            shippingFee: currentDefaultFee,
            geometryData: geometryData,
            layer: layer,
          };
          tempDrawings.push(tempDrawing);
          drawingCounter++;
          updateTempDrawingsList();
          // Tăng defaultShippingFee lên 5000 so với giá vừa vẽ
          document.getElementById('defaultShippingFee').value = currentDefaultFee + 5000;
        });

        // Mouse coordinate tracking
        map.on('mousemove', function (e) {
          document.getElementById('mouseCoords').textContent = `${e.latlng.lat.toFixed(6)}, ${e.latlng.lng.toFixed(6)}`;
        });
      }

      // LocalStorage functions
      function saveLoginData() {
        const rememberMe = document.getElementById('rememberMe').checked;

        if (rememberMe) {
          localStorage.setItem(STORAGE_KEYS.API_BASE_URL, document.getElementById('apiBaseUrl').value);
          localStorage.setItem(STORAGE_KEYS.LOGIN_PHONE, document.getElementById('loginPhone').value);
          localStorage.setItem(STORAGE_KEYS.LOGIN_PASSWORD, document.getElementById('loginPassword').value);
          localStorage.setItem(STORAGE_KEYS.REMEMBER_ME, 'true');
        } else {
          // Clear saved data if remember me is unchecked
          Object.values(STORAGE_KEYS).forEach((key) => localStorage.removeItem(key));
        }
      }

      function loadSavedData() {
        const rememberMe = localStorage.getItem(STORAGE_KEYS.REMEMBER_ME) === 'true';

        if (rememberMe) {
          const savedApiUrl = localStorage.getItem(STORAGE_KEYS.API_BASE_URL);
          const savedPhone = localStorage.getItem(STORAGE_KEYS.LOGIN_PHONE);
          const savedPassword = localStorage.getItem(STORAGE_KEYS.LOGIN_PASSWORD);

          if (savedApiUrl) document.getElementById('apiBaseUrl').value = savedApiUrl;
          if (savedPhone) document.getElementById('loginPhone').value = savedPhone;
          if (savedPassword) document.getElementById('loginPassword').value = savedPassword;

          document.getElementById('rememberMe').checked = true;
        } else {
          document.getElementById('loginPhone').value = '<EMAIL>';
          document.getElementById('loginPassword').value = '123456';
        }
      }

      function clearSavedData() {
        if (confirm('Are you sure you want to clear all saved login data?')) {
          Object.values(STORAGE_KEYS).forEach((key) => localStorage.removeItem(key));

          // Reset form
          document.getElementById('apiBaseUrl').value = window.location.origin;
          document.getElementById('loginPhone').value = '';
          document.getElementById('loginPassword').value = '';
          document.getElementById('rememberMe').checked = false;

          showToast('Saved data cleared successfully!', 'success');
        }
      }

      // Auto-save on input changes
      function setupAutoSave() {
        const inputs = ['apiBaseUrl', 'loginPhone', 'loginPassword'];
        inputs.forEach((inputId) => {
          document.getElementById(inputId).addEventListener('input', function () {
            if (document.getElementById('rememberMe').checked) {
              saveLoginData();
            }
          });
        });

        document.getElementById('rememberMe').addEventListener('change', function () {
          if (this.checked) {
            saveLoginData();
          } else {
            Object.values(STORAGE_KEYS).forEach((key) => localStorage.removeItem(key));
          }
        });

        // Auto-location preference
        document.getElementById('autoLocation').addEventListener('change', function () {
          localStorage.setItem('geofencing_auto_location', this.checked.toString());
        });
      }

      // Authentication functions
      async function login() {
        const phone = document.getElementById('loginPhone').value;
        const password = document.getElementById('loginPassword').value;
        apiBaseUrl = document.getElementById('apiBaseUrl').value;

        if (!phone || !password) {
          showToast('Please enter phone/email and password', 'warning');
          return;
        }

        try {
          const response = await fetch(`${apiBaseUrl}/auth/merchant-user/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: phone,
              password: password,
            }),
          });

          const data = await response.json();

          if (response.ok && data.accessToken) {
            authToken = data.accessToken;
            document.getElementById('loginStatus').className = 'status-indicator status-logged-in';
            document.getElementById('loginFormContainer').classList.add('collapsed'); // Collapse login form
            document.querySelector('.toggle-arrow').classList.add('rotated'); // Rotate arrow
            document.getElementById('loggedInInfo').style.display = 'block';
            document.getElementById('userInfo').textContent = data.user?.name || phone;

            // Save login data if remember me is checked
            saveLoginData();

            // Load restaurants after login
            await loadRestaurants();
            updateApiResponse('Login successful', data);

            // Focus on restaurant selection after successful login
            setTimeout(() => {
              document.getElementById('restaurantSelect').focus();
            }, 300);
          } else {
            throw new Error(data.message || 'Login failed');
          }
        } catch (error) {
          showToast('Login failed: ' + error.message, 'error');
          updateApiResponse('Login failed', error);
        }
      }

      function logout() {
        authToken = null;
        currentRestaurantId = null;
        document.getElementById('loginStatus').className = 'status-indicator status-logged-out';
        document.getElementById('loginFormContainer').classList.remove('collapsed'); // Expand login form
        document.querySelector('.toggle-arrow').classList.remove('rotated'); // Rotate arrow back
        document.getElementById('loggedInInfo').style.display = 'none';
        document.getElementById('restaurantSelect').innerHTML =
          '<option value="">🏪 Select a restaurant to start...</option>';
        clearMap();

        // Don't clear saved login data on logout - only clear session data
        updateApiResponse('Logged out', {});

        // Focus on login form after logout
        setTimeout(() => {
          const loginPhone = document.getElementById('loginPhone');
          if (loginPhone) {
            loginPhone.focus();
          }
        }, 100);
      }

      // Restaurant functions
      async function loadRestaurants() {
        if (!authToken) {
          showToast('Please login first', 'warning');
          return;
        }

        try {
          const response = await fetch(`${apiBaseUrl}/restaurants?page=1&limit=200`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          const data = await response.json();

          if (response.ok) {
            const select = document.getElementById('restaurantSelect');
            select.innerHTML = '<option value="">🏪 Select a restaurant to start...</option>';

            data.items.forEach((restaurant) => {
              const option = document.createElement('option');
              option.value = restaurant.id;
              option.textContent = `${restaurant.name} (${restaurant.address})`;
              select.appendChild(option);
            });

            updateApiResponse('Restaurants loaded', data);

            // Focus on restaurant select after loading restaurants
            setTimeout(() => {
              select.focus();
            }, 100);
          } else {
            throw new Error(data.message || 'Failed to load restaurants');
          }
        } catch (error) {
          showToast('Failed to load restaurants: ' + error.message, 'error');
          updateApiResponse('Load restaurants failed', error);
        }
      }

      async function loadRestaurantGeofencing() {
        const restaurantId = document.getElementById('restaurantSelect').value;
        if (!restaurantId || !authToken) return;

        currentRestaurantId = restaurantId;

        try {
          const response = await fetch(`${apiBaseUrl}/geofencing/restaurant/${restaurantId}`, {
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          });

          const data = await response.json();

          if (response.ok) {
            currentGeofencing = data.map((item) => ({
              id: item.id,
              name: item.name,
              description: item.description,
              type: item.type,
              shippingFee: item.shippingFee,
              geometryData: item.geometryData,
            }));

            displayGeofencingOnMap();
            updateGeofencingList();
            updateApiResponse('Geofencing loaded', data);

            // Auto-check current location if available
            const currentLat = document.getElementById('currentLat').textContent;
            const currentLng = document.getElementById('currentLng').textContent;
            if (currentLat !== '-' && currentLng !== '-') {
              checkCurrentLocationInGeofencing(parseFloat(currentLat), parseFloat(currentLng));
            }
          } else {
            throw new Error(data.message || 'Failed to load geofencing');
          }
        } catch (error) {
          showToast('Failed to load geofencing: ' + error.message, 'error');
          updateApiResponse('Load geofencing failed', error);
        }
      }

      // Drawing functions
      function updateTempDrawingsList() {
        const listDiv = document.getElementById('tempDrawingsList');
        const countSpan = document.getElementById('tempDrawingCount');
        const section = document.getElementById('tempDrawingsSection');

        countSpan.textContent = tempDrawings.length;

        if (tempDrawings.length > 0) {
          section.style.display = 'block';
        } else {
          section.style.display = 'none';
        }

        listDiv.innerHTML = '';

        tempDrawings.forEach((drawing, index) => {
          const div = document.createElement('div');
          div.className = 'temp-drawing-item';
          if (editingTempDrawingIndex === index) {
            // Hiển thị form sửa
            div.innerHTML = `
                        <form onsubmit="saveEditTempDrawing(event, ${index})">
                            <div>
                                <span class="temp-drawing-type type-${drawing.type.toLowerCase()}">${drawing.type}</span>
                                <input type="text" class="form-control form-control-sm d-inline w-auto" style="width: 120px; display: inline-block; margin-left: 5px;" id="editName${index}" value="${drawing.name}" required />
                            </div>
                            <div class="mt-1">
                                <span>Fee: </span>
                                <input type="number" class="form-control form-control-sm d-inline w-auto" style="width: 90px; display: inline-block;" id="editFee${index}" value="${drawing.shippingFee}" min="0" required /> VND
                            </div>
                            <div class="temp-drawing-controls mt-1">
                                <button type="submit" class="btn btn-sm btn-success">Save</button>
                                <button type="button" class="btn btn-sm btn-secondary" onclick="cancelEditTempDrawing()">Cancel</button>
                            </div>
                        </form>
                    `;
          } else {
            div.innerHTML = `
                        <div>
                            <span class="temp-drawing-type type-${drawing.type.toLowerCase()}">${drawing.type}</span>
                            <strong>${drawing.name}</strong>
                        </div>
                        <div>Fee: ${drawing.shippingFee.toLocaleString()} VND</div>
                        <div class="temp-drawing-controls">
                            <button class="btn btn-sm btn-outline-primary" onclick="editTempDrawing(${index})">Edit</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeTempDrawing(${index})">Remove</button>
                        </div>
                    `;
            // Click to highlight on map
            div.addEventListener('click', function () {
              highlightTempDrawing(index);
            });
          }
          listDiv.appendChild(div);
        });
      }

      function editTempDrawing(index) {
        editingTempDrawingIndex = index;
        updateTempDrawingsList();
      }

      function saveEditTempDrawing(event, index) {
        event.preventDefault();
        const nameInput = document.getElementById(`editName${index}`);
        const feeInput = document.getElementById(`editFee${index}`);
        const newName = nameInput.value.trim();
        const newFee = feeInput.value.trim();
        if (!newName) {
          showToast('Name cannot be empty!', 'warning');
          nameInput.focus();
          return;
        }
        if (!/^[0-9]+$/.test(newFee) || parseInt(newFee) < 0) {
          showToast('Shipping fee must be a non-negative number!', 'warning');
          feeInput.focus();
          return;
        }
        tempDrawings[index].name = newName;
        tempDrawings[index].shippingFee = parseInt(newFee);
        editingTempDrawingIndex = null;
        updateTempDrawingsList();
      }

      function cancelEditTempDrawing() {
        editingTempDrawingIndex = null;
        updateTempDrawingsList();
      }

      function removeTempDrawing(index) {
        const drawing = tempDrawings[index];

        // Remove layer from map
        drawnItems.removeLayer(drawing.layer);

        // Remove from array
        tempDrawings.splice(index, 1);

        updateTempDrawingsList();
      }

      function highlightTempDrawing(index) {
        const drawing = tempDrawings[index];

        // Remove existing highlights
        document.querySelectorAll('.temp-drawing-item').forEach((item) => {
          item.classList.remove('selected');
        });

        // Highlight current item
        document.querySelectorAll('.temp-drawing-item')[index].classList.add('selected');

        // Zoom to layer
        if (drawing.layer) {
          map.fitBounds(drawing.layer.getBounds(), { padding: [20, 20] });
        }
      }

      function clearTempDrawings() {
        if (tempDrawings.length === 0) {
          showToast('No temporary drawings to save', 'warning');
          return;
        }

        if (confirm(`Clear all ${tempDrawings.length} temporary drawings?`)) {
          // Remove all layers from map
          tempDrawings.forEach((drawing) => {
            drawnItems.removeLayer(drawing.layer);
          });

          // Clear array
          tempDrawings = [];
          drawingCounter = 1;

          updateTempDrawingsList();
        }
      }

      function saveAllTempDrawings() {
        if (tempDrawings.length === 0) {
          showToast('No temporary drawings to save', 'warning');
          return;
        }

        // Convert temp drawings to geofencing format
        const newGeofencings = tempDrawings.map((drawing) => ({
          name: drawing.name,
          description: drawing.description,
          type: drawing.type.toLowerCase(),
          shippingFee: drawing.shippingFee,
          geometryData: drawing.geometryData,
        }));

        // Add to current geofencing
        currentGeofencing.push(...newGeofencings);

        // Clear temp drawings
        tempDrawings = [];
        drawingCounter = 1;

        // Update UI
        updateTempDrawingsList();
        updateGeofencingList();

        showToast(`Successfully saved ${newGeofencings.length} geofencing areas!`, 'success');
      }

      function layerToGeometryData(layer, fillColor) {
        let geometryData = {
          fillColor: fillColor,
          snapshot: {},
        };

        if (layer instanceof L.Polygon && !(layer instanceof L.Rectangle)) {
          geometryData.type = 'polygon';
          geometryData.snapshot.path = layer.getLatLngs()[0].map((latlng) => ({
            lat: latlng.lat,
            lng: latlng.lng,
          }));
        } else if (layer instanceof L.Circle) {
          geometryData.type = 'circle';
          geometryData.snapshot.center = {
            lat: layer.getLatLng().lat,
            lng: layer.getLatLng().lng,
          };
          geometryData.snapshot.radius = layer.getRadius();
        } else if (layer instanceof L.Rectangle) {
          geometryData.type = 'rectangle';
          const bounds = layer.getBounds();
          geometryData.snapshot.bounds = {
            south: bounds.getSouth(),
            west: bounds.getWest(),
            north: bounds.getNorth(),
            east: bounds.getEast(),
          };
        }

        return geometryData;
      }

      function displayGeofencingOnMap() {
        drawnItems.clearLayers();

        currentGeofencing.forEach((geofencing, index) => {
          const layer = geometryDataToLayer(geofencing.geometryData);
          if (layer) {
            layer.setStyle({
              fillColor: geofencing.geometryData.fillColor,
              fillOpacity: 0.3,
              color: geofencing.geometryData.fillColor,
              weight: 2,
            });

            layer.bindPopup(`
                        <strong>${geofencing.name}</strong><br>
                        Type: ${geofencing.type}<br>
                        Fee: ${geofencing.shippingFee.toLocaleString()} VND<br>
                        ${geofencing.description ? `Description: ${geofencing.description}<br>` : ''}
                        <button onclick="removeGeofencing(${index})" class="btn btn-sm btn-danger">Remove</button>
                    `);

            drawnItems.addLayer(layer);
          }
        });
      }

      function geometryDataToLayer(geometryData) {
        const { type, snapshot } = geometryData;

        switch (type) {
          case 'polygon':
            return L.polygon(snapshot.path.map((p) => [p.lat, p.lng]));
          case 'circle':
            return L.circle([snapshot.center.lat, snapshot.center.lng], {
              radius: snapshot.radius,
            });
          case 'rectangle':
            return L.rectangle([
              [snapshot.bounds.south, snapshot.bounds.west],
              [snapshot.bounds.north, snapshot.bounds.east],
            ]);
          default:
            return null;
        }
      }

      function clearMap() {
        drawnItems.clearLayers();

        // Clear current geofencing
        currentGeofencing = [];
        updateGeofencingList();

        // Clear temp drawings
        tempDrawings = [];
        drawingCounter = 1;
        updateTempDrawingsList();

        // Clear current location marker
        if (currentLocationMarker) {
          map.removeLayer(currentLocationMarker);
          currentLocationMarker = null;
        }

        // Reset location info
        const locationInfo = document.getElementById('locationInfo');
        locationInfo.style.display = 'none';
        document.getElementById('currentLat').textContent = '-';
        document.getElementById('currentLng').textContent = '-';
        document.getElementById('currentAccuracy').textContent = '-';
        document.getElementById('locationBtnText').textContent = 'Get My Location';

        // Hide current location display
        document.getElementById('currentLocationDisplay').style.display = 'none';
        document.getElementById('myLocationCoords').textContent = '-';

        // Clear geofencing status
        const statusDiv = locationInfo.querySelector('.geofencing-status');
        if (statusDiv) {
          statusDiv.innerHTML = '';
        }
      }

      function removeGeofencing(index) {
        currentGeofencing.splice(index, 1);
        displayGeofencingOnMap();
        updateGeofencingList();
      }

      function editGeofencing(index) {
        const geofencing = currentGeofencing[index];
        const listDiv = document.getElementById('geofencingList');
        const geofencingItem = listDiv.children[index];

        // Create edit form
        geofencingItem.innerHTML = `
                <form onsubmit="saveEditGeofencing(event, ${index})">
                    <div class="mb-2">
                        <label class="form-label small">Name</label>
                        <input type="text" class="form-control form-control-sm" id="editGeofencingName${index}" value="${geofencing.name}" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small">Description</label>
                        <textarea class="form-control form-control-sm" id="editGeofencingDesc${index}" rows="2">${geofencing.description || ''}</textarea>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small">Shipping Fee (VND)</label>
                        <input type="number" class="form-control form-control-sm" id="editGeofencingFee${index}" value="${geofencing.shippingFee}" min="0" required>
                    </div>
                    <div class="mb-2">
                        <label class="form-label small">Geometry</label>
                        <div class="d-flex gap-1">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="redrawGeofencing(${index})">Redraw Shape</button>
                            <button type="button" class="btn btn-sm btn-outline-warning" onclick="moveGeofencing(${index})">Move Shape</button>
                        </div>
                    </div>
                    <div class="d-flex gap-1">
                        <button type="submit" class="btn btn-sm btn-success">Save</button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="cancelEditGeofencing(${index})">Cancel</button>
                    </div>
                </form>
            `;

        // Focus on name field
        setTimeout(() => {
          document.getElementById(`editGeofencingName${index}`).focus();
        }, 100);
      }

      function saveEditGeofencing(event, index) {
        event.preventDefault();

        const nameInput = document.getElementById(`editGeofencingName${index}`);
        const descInput = document.getElementById(`editGeofencingDesc${index}`);
        const feeInput = document.getElementById(`editGeofencingFee${index}`);

        const newName = nameInput.value.trim();
        const newDesc = descInput.value.trim();
        const newFee = parseInt(feeInput.value);

        // Validation
        if (!newName) {
          showToast('Name cannot be empty!', 'warning');
          nameInput.focus();
          return;
        }

        if (isNaN(newFee) || newFee < 0) {
          showToast('Shipping fee must be a non-negative number!', 'warning');
          feeInput.focus();
          return;
        }

        // Update geofencing data
        currentGeofencing[index].name = newName;
        currentGeofencing[index].description = newDesc;
        currentGeofencing[index].shippingFee = newFee;

        // Update map display
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Geofencing updated successfully!', 'success');
      }

      function cancelEditGeofencing(index) {
        // Just refresh the list to show original data
        updateGeofencingList();
      }

      function redrawGeofencing(index) {
        const geofencing = currentGeofencing[index];

        // Remove current geofencing from map temporarily
        displayGeofencingOnMap();

        // Show instruction
        showToast('Draw new shape on the map. Click "Save New Shape" when done.', 'info');

        // Create a temporary drawing mode
        const tempDrawing = {
          id: `redraw_${index}`,
          originalIndex: index,
          isRedrawing: true,
        };

        // Add to temp drawings for processing
        tempDrawings.push(tempDrawing);

        // Update UI to show redraw mode
        updateRedrawMode(index);
      }

      function updateRedrawMode(index) {
        const listDiv = document.getElementById('geofencingList');
        const geofencingItem = listDiv.children[index];

        geofencingItem.innerHTML = `
                <div class="alert alert-info alert-sm mb-2">
                    <strong>🔄 Redrawing Mode</strong><br>
                    Draw new shape on the map, then click "Save New Shape"
                </div>
                <div class="d-flex gap-1">
                    <button type="button" class="btn btn-sm btn-success" onclick="saveRedrawnGeofencing(${index})">Save New Shape</button>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="cancelRedrawGeofencing(${index})">Cancel Redraw</button>
                </div>
            `;
      }

      function saveRedrawnGeofencing(index) {
        // Find the redraw temp drawing
        const redrawDrawing = tempDrawings.find((d) => d.id === `redraw_${index}`);
        if (!redrawDrawing) {
          showToast('No new shape found. Please draw a shape first.', 'warning');
          return;
        }

        // Get the latest drawn layer
        const layers = drawnItems.getLayers();
        const newLayer = layers[layers.length - 1];

        if (!newLayer) {
          showToast('No new shape found. Please draw a shape first.', 'warning');
          return;
        }

        // Convert layer to geometry data
        const geometryData = layerToGeometryData(newLayer, currentGeofencing[index].geometryData.fillColor);

        // Update geofencing geometry
        currentGeofencing[index].geometryData = geometryData;

        // Remove temp drawing
        const redrawIndex = tempDrawings.findIndex((d) => d.id === `redraw_${index}`);
        if (redrawIndex !== -1) {
          tempDrawings.splice(redrawIndex, 1);
        }

        // Remove the new layer from drawn items
        drawnItems.removeLayer(newLayer);

        // Update display
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Shape updated successfully!', 'success');
      }

      function cancelRedrawGeofencing(index) {
        // Remove temp drawing
        const redrawIndex = tempDrawings.findIndex((d) => d.id === `redraw_${index}`);
        if (redrawIndex !== -1) {
          tempDrawings.splice(redrawIndex, 1);
        }

        // Remove any new layers that might have been drawn
        const layers = drawnItems.getLayers();
        if (layers.length > currentGeofencing.length) {
          drawnItems.removeLayer(layers[layers.length - 1]);
        }

        // Update display
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Redraw cancelled.', 'info');
      }

      function moveGeofencing(index) {
        const geofencing = currentGeofencing[index];

        // Get the current layer
        const layers = drawnItems.getLayers();
        const currentLayer = layers[index];

        if (currentLayer) {
          // Create a temporary feature group for editing
          const editFeatureGroup = new L.FeatureGroup([currentLayer]);

          // Remove the layer from drawnItems temporarily
          drawnItems.removeLayer(currentLayer);

          // Add it to the edit feature group
          editFeatureGroup.addLayer(currentLayer);

          // Create edit control
          const editControl = new L.Control.Draw({
            edit: {
              featureGroup: editFeatureGroup,
              remove: false,
            },
            draw: false,
          });

          map.addControl(editControl);

          // Store reference for later use
          window.currentEditControl = editControl;
          window.currentEditFeatureGroup = editFeatureGroup;
          window.currentEditIndex = index;

          // Show instruction
          showToast('Drag the shape to move it. Click "Save Position" when done.', 'info');

          // Update UI to show move mode
          updateMoveMode(index, editControl);
        }
      }

      function updateMoveMode(index, editControl) {
        const listDiv = document.getElementById('geofencingList');
        const geofencingItem = listDiv.children[index];

        geofencingItem.innerHTML = `
                <div class="alert alert-warning alert-sm mb-2">
                    <strong>📌 Moving Mode</strong><br>
                    Drag the shape on the map, then click "Save Position"
                </div>
                <div class="d-flex gap-1">
                    <button type="button" class="btn btn-sm btn-success" onclick="saveMovedGeofencing(${index}, '${editControl._leaflet_id}')">Save Position</button>
                    <button type="button" class="btn btn-sm btn-secondary" onclick="cancelMoveGeofencing(${index}, '${editControl._leaflet_id}')">Cancel Move</button>
                </div>
            `;
      }

      function saveMovedGeofencing(index, editControlId) {
        // Get the updated layer from edit feature group
        const updatedLayer = window.currentEditFeatureGroup.getLayers()[0];

        if (updatedLayer) {
          // Convert updated layer to geometry data
          const geometryData = layerToGeometryData(updatedLayer, currentGeofencing[index].geometryData.fillColor);

          // Update geofencing geometry
          currentGeofencing[index].geometryData = geometryData;
        }

        // Clean up edit control
        if (window.currentEditControl) {
          map.removeControl(window.currentEditControl);
          window.currentEditControl = null;
        }

        // Clean up edit feature group
        if (window.currentEditFeatureGroup) {
          map.removeLayer(window.currentEditFeatureGroup);
          window.currentEditFeatureGroup = null;
        }

        // Update display
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Position updated successfully!', 'success');
      }

      function cancelMoveGeofencing(index, editControlId) {
        // Clean up edit control
        if (window.currentEditControl) {
          map.removeControl(window.currentEditControl);
          window.currentEditControl = null;
        }

        // Clean up edit feature group
        if (window.currentEditFeatureGroup) {
          map.removeLayer(window.currentEditFeatureGroup);
          window.currentEditFeatureGroup = null;
        }

        // Update display to restore original position
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Move cancelled.', 'info');
      }

      function duplicateGeofencing(index) {
        const original = currentGeofencing[index];

        // Create a deep copy of the geofencing
        const duplicated = {
          name: `${original.name} (Copy)`,
          description: original.description,
          type: original.type,
          shippingFee: original.shippingFee,
          geometryData: JSON.parse(JSON.stringify(original.geometryData)), // Deep copy
        };

        // Add to current geofencing
        currentGeofencing.push(duplicated);

        // Update display
        displayGeofencingOnMap();
        updateGeofencingList();

        showToast('Geofencing duplicated successfully!', 'success');
      }

      function exportGeofencing() {
        if (currentGeofencing.length === 0) {
          showToast('No geofencing data to export', 'warning');
          return;
        }

        const exportData = {
          restaurantId: currentRestaurantId,
          geofencingAreas: currentGeofencing,
          exportedAt: new Date().toISOString(),
        };

        const dataStr = JSON.stringify(exportData, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `geofencing_${currentRestaurantId}_${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        showToast('Geofencing data exported successfully!', 'success');
      }

      function importGeofencing() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = function (e) {
          const file = e.target.files[0];
          if (!file) return;

          const reader = new FileReader();
          reader.onload = function (e) {
            try {
              const importData = JSON.parse(e.target.result);

              if (!importData.geofencingAreas || !Array.isArray(importData.geofencingAreas)) {
                throw new Error('Invalid geofencing data format');
              }

              // Confirm import
              if (
                confirm(`Import ${importData.geofencingAreas.length} geofencing areas? This will replace current data.`)
              ) {
                currentGeofencing = importData.geofencingAreas;
                displayGeofencingOnMap();
                updateGeofencingList();

                showToast(`Imported ${importData.geofencingAreas.length} geofencing areas successfully!`, 'success');
              }
            } catch (error) {
              showToast('Failed to import geofencing data: ' + error.message, 'error');
            }
          };
          reader.readAsText(file);
        };

        input.click();
      }

      function clearAllGeofencing() {
        if (currentGeofencing.length === 0) {
          showToast('No geofencing data to clear', 'warning');
          return;
        }

        if (confirm(`Clear all ${currentGeofencing.length} geofencing areas?`)) {
          currentGeofencing = [];
          displayGeofencingOnMap();
          updateGeofencingList();

          showToast('All geofencing areas cleared!', 'success');
        }
      }

      function updateGeofencingList() {
        const listDiv = document.getElementById('geofencingList');
        const countSpan = document.getElementById('geofencingCount');
        const submitBtn = document.getElementById('submitBtn');

        countSpan.textContent = currentGeofencing.length;
        submitBtn.disabled = !currentRestaurantId || currentGeofencing.length === 0;

        listDiv.innerHTML = '';

        currentGeofencing.forEach((geofencing, index) => {
          const div = document.createElement('div');
          div.className = 'geofencing-item';
          div.innerHTML = `
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <strong>${geofencing.name}</strong>
                            <span class="badge bg-${geofencing.type === 'polygon' ? 'primary' : geofencing.type === 'circle' ? 'success' : 'warning'}">${geofencing.type}</span>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="editGeofencing(${index})" title="Edit">✏️</button>
                            <button class="btn btn-sm btn-outline-info me-1" onclick="duplicateGeofencing(${index})" title="Duplicate">📋</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="removeGeofencing(${index})" title="Remove">🗑️</button>
                        </div>
                    </div>
                    <div class="small text-muted">
                        Fee: ${geofencing.shippingFee.toLocaleString()} VND
                        ${geofencing.description ? `<br>Description: ${geofencing.description}` : ''}
                    </div>
                `;
          listDiv.appendChild(div);
        });
      }

      async function submitAllGeofencing() {
        if (!currentRestaurantId || !authToken) {
          showToast('Please select a restaurant and login first', 'warning');
          return;
        }
        const payload = {
          restaurantId: currentRestaurantId,
          geofencingAreas: currentGeofencing,
        };

        try {
          const response = await fetch(`${apiBaseUrl}/geofencing`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${authToken}`,
            },
            body: JSON.stringify(payload),
          });

          const data = await response.json();

          if (response.ok) {
            showToast('Geofencing updated successfully!', 'success');
            updateApiResponse('Geofencing update successful', data);

            // Reload geofencing from server
            await loadRestaurantGeofencing();
          } else {
            throw new Error(data.message || 'Failed to update geofencing');
          }
        } catch (error) {
          showToast('Failed to update geofencing: ' + error.message, 'error');
          updateApiResponse('Geofencing update failed', error);
        }
      }

      function updateApiResponse(title, data) {
        const textarea = document.getElementById('apiResponse');
        const timestamp = new Date().toLocaleTimeString();
        textarea.value = `[${timestamp}] ${title}\n${JSON.stringify(data, null, 2)}\n\n${textarea.value}`;
      }

      // Toggle login panel visibility
      function toggleLoginPanel() {
        const container = document.getElementById('loginFormContainer');
        const arrow = document.querySelector('.toggle-arrow');

        if (container.classList.contains('collapsed')) {
          container.classList.remove('collapsed');
          arrow.classList.remove('rotated');

          // Focus on login phone/email field when expanding
          setTimeout(() => {
            const loginPhone = document.getElementById('loginPhone');
            if (loginPhone && !authToken) {
              loginPhone.focus();
            }
          }, 300);
        } else {
          container.classList.add('collapsed');
          arrow.classList.add('rotated');
        }
      }

      // My Location functions
      function getMyLocation() {
        const locationBtnText = document.getElementById('locationBtnText');
        const locationInfo = document.getElementById('locationInfo');

        if (!navigator.geolocation) {
          showToast('Geolocation is not supported by your browser.', 'error');
          return;
        }

        locationBtnText.textContent = 'Getting location...';

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const lat = position.coords.latitude;
            const lng = position.coords.longitude;
            const accuracy = position.coords.accuracy;

            // Update display
            document.getElementById('currentLat').textContent = lat.toFixed(6);
            document.getElementById('currentLng').textContent = lng.toFixed(6);
            document.getElementById('currentAccuracy').textContent = accuracy.toFixed(0);
            locationInfo.style.display = 'block';
            locationBtnText.textContent = '✅ Location Found';

            // Update current location display in coordinates area
            document.getElementById('myLocationCoords').textContent = `${lat.toFixed(6)}, ${lng.toFixed(6)}`;
            document.getElementById('currentLocationDisplay').style.display = 'inline';

            // Remove existing location marker
            if (currentLocationMarker) {
              map.removeLayer(currentLocationMarker);
            }

            // Add new location marker
            currentLocationMarker = L.marker([lat, lng], {
              icon: L.icon({
                iconUrl:
                  'data:image/svg+xml;base64,' +
                  btoa(`
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="red" width="24" height="24">
                                    <circle cx="12" cy="12" r="8" fill="red" stroke="white" stroke-width="2"/>
                                    <circle cx="12" cy="12" r="3" fill="white"/>
                                </svg>
                            `),
                iconSize: [24, 24],
                iconAnchor: [12, 12],
                popupAnchor: [0, -12],
              }),
            }).addTo(map);

            // Add popup with location info
            currentLocationMarker
              .bindPopup(
                `
                        <strong>📍 Your Location</strong><br/>
                        Lat: ${lat.toFixed(6)}<br/>
                        Lng: ${lng.toFixed(6)}<br/>
                        Accuracy: ±${accuracy.toFixed(0)}m
                    `,
              )
              .openPopup();

            // Center map on current location
            map.setView([lat, lng], Math.max(map.getZoom(), 15));

            // Check if in any geofencing area
            checkCurrentLocationInGeofencing(lat, lng);

            // Save preference
            localStorage.setItem('geofencing_auto_location', 'true');
          },
          (error) => {
            let errorMessage = 'Unknown error';
            switch (error.code) {
              case error.PERMISSION_DENIED:
                errorMessage = 'Location access denied. Please enable location permission.';
                break;
              case error.POSITION_UNAVAILABLE:
                errorMessage = 'Location information is unavailable.';
                break;
              case error.TIMEOUT:
                errorMessage = 'Location request timed out.';
                break;
            }
            showToast('Error getting location: ' + errorMessage, 'error');
            locationBtnText.textContent = '❌ Get My Location';
            locationInfo.style.display = 'none';
            localStorage.setItem('geofencing_auto_location', 'false');
          },
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000, // 5 minutes cache
          },
        );
      }

      function centerMapOnMyLocation() {
        const currentLat = document.getElementById('currentLat').textContent;
        const currentLng = document.getElementById('currentLng').textContent;

        if (currentLat !== '-' && currentLng !== '-') {
          map.setView([parseFloat(currentLat), parseFloat(currentLng)], 15);
          if (currentLocationMarker) {
            currentLocationMarker.openPopup();
          }
        } else {
          showToast('Please get your location first.', 'warning');
        }
      }

      function checkCurrentLocationInGeofencing(lat, lng) {
        if (!currentGeofencing || currentGeofencing.length === 0) {
          return;
        }

        const point = L.latLng(lat, lng);
        const insideAreas = [];

        currentGeofencing.forEach((area) => {
          let isInside = false;

          switch (area.type) {
            case 'polygon':
              if (area.geometryData && area.geometryData.path) {
                // Use point-in-polygon algorithm
                isInside = pointInPolygon(
                  [lng, lat],
                  area.geometryData.path.map((p) => [p.lng, p.lat]),
                );
              }
              break;
            case 'circle':
              if (area.geometryData && area.geometryData.center && area.geometryData.radius) {
                const center = L.latLng(area.geometryData.center.lat, area.geometryData.center.lng);
                const distance = point.distanceTo(center);
                isInside = distance <= area.geometryData.radius;
              }
              break;
            case 'rectangle':
              if (area.geometryData && area.geometryData.bounds) {
                const bounds = area.geometryData.bounds;
                isInside = lat >= bounds.south && lat <= bounds.north && lng >= bounds.west && lng <= bounds.east;
              }
              break;
          }

          if (isInside) {
            insideAreas.push(area);
          }
        });

        // Update UI with geofencing status
        showGeofencingStatus(insideAreas);
      }

      // Point in polygon algorithm (ray casting)
      function pointInPolygon(point, polygon) {
        const x = point[0],
          y = point[1];
        let inside = false;

        for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
          const xi = polygon[i][0],
            yi = polygon[i][1];
          const xj = polygon[j][0],
            yj = polygon[j][1];

          if (yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi) {
            inside = !inside;
          }
        }

        return inside;
      }

      function showGeofencingStatus(insideAreas) {
        let statusHtml = '';

        if (insideAreas.length > 0) {
          statusHtml =
            '<div class="alert alert-success alert-sm mt-2 mb-0"><strong>✅ Inside Geofencing:</strong><br/>';
          insideAreas.forEach((area) => {
            statusHtml += `• ${area.name} (${area.shippingFee.toLocaleString()} VND)<br/>`;
          });
          statusHtml += '</div>';
        } else if (currentGeofencing.length > 0) {
          statusHtml =
            '<div class="alert alert-warning alert-sm mt-2 mb-0"><strong>⚠️ Outside all geofencing areas</strong></div>';
        }

        // Add or update status in location info
        const locationInfo = document.getElementById('locationInfo');
        let statusDiv = locationInfo.querySelector('.geofencing-status');
        if (!statusDiv) {
          statusDiv = document.createElement('div');
          statusDiv.className = 'geofencing-status';
          locationInfo.appendChild(statusDiv);
        }
        statusDiv.innerHTML = statusHtml;
      }

      // Toast message
      function showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        let bg = '#007bff';
        if (type === 'success') bg = '#28a745';
        else if (type === 'error') bg = '#dc3545';
        else if (type === 'warning') bg = '#ffc107';
        toast.style.background = bg;
        toast.style.color = '#fff';
        toast.style.padding = '12px 20px';
        toast.style.borderRadius = '6px';
        toast.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
        toast.style.marginTop = '10px';
        toast.style.fontSize = '15px';
        toast.style.opacity = '0.97';
        toast.style.transition = 'opacity 0.3s';
        toast.innerText = message;
        toastContainer.appendChild(toast);
        setTimeout(() => {
          toast.style.opacity = '0';
          setTimeout(() => toast.remove(), 300);
        }, 3000);
      }

      // Initialize on page load
      window.onload = function () {
        initMap();

        // Set default API URL if no saved data
        document.getElementById('apiBaseUrl').value = window.location.origin;

        // Load saved login data
        loadSavedData();

        // Setup auto-save functionality
        setupAutoSave();

        // Smart focus logic
        setTimeout(() => {
          if (!authToken) {
            // If not logged in, focus on login phone/email field
            const loginPhone = document.getElementById('loginPhone');
            if (loginPhone && !loginPhone.value) {
              loginPhone.focus();
            }
          } else {
            // If logged in, focus on restaurant selection
            document.getElementById('restaurantSelect').focus();
          }
        }, 100);

        // Load auto-location preference
        const autoLocationPref = localStorage.getItem('geofencing_auto_location') === 'true';
        const autoLocationCheckbox = document.getElementById('autoLocation');
        autoLocationCheckbox.checked = autoLocationPref;

        // Auto-get location if preference is enabled
        if (autoLocationPref) {
          setTimeout(() => {
            getMyLocation();
          }, 1000); // Delay to let map initialize
        }
      };
    </script>
  </body>
</html>
