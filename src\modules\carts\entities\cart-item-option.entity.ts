import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { MenuItemOptionGroup } from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItemOption } from '@/modules/menu-item-options/entities/menu-item-option.entity';

import { CartItem } from './cart-item.entity';

@Entity('cart_item_options')
@Index(['cartItemId', 'menuItemOptionGroupId', 'menuItemOptionId'], { unique: true, where: 'deleted_at IS NULL' })
export class CartItemOption extends BaseEntity {
  @Column({ name: 'cart_item_id', type: 'uuid' })
  cartItemId: string;

  @Column({ name: 'menu_item_option_group_id', type: 'uuid' })
  menuItemOptionGroupId: string;

  @Column({ name: 'menu_item_option_id', type: 'uuid' })
  menuItemOptionId: string;

  @Column({ name: 'amount', type: 'integer', default: 1 })
  amount: number;

  @ManyToOne(() => CartItem, (cartItem) => cartItem.cartItemOptions, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'cart_item_id' })
  cartItem: WrapperType<CartItem>;

  @ManyToOne(() => MenuItemOptionGroup)
  @JoinColumn({ name: 'menu_item_option_group_id' })
  menuItemOptionGroup?: WrapperType<MenuItemOptionGroup>;

  @ManyToOne(() => MenuItemOption)
  @JoinColumn({ name: 'menu_item_option_id' })
  menuItemOption?: WrapperType<MenuItemOption>;
}
