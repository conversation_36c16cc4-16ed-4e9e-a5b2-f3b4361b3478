import { isNil } from 'lodash';
import { IPaginationOptions, paginate, Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, IsNull, Repository, SelectQueryBuilder } from 'typeorm';

import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import {
  MenuItemOptionGroup,
  MenuItemOptionGroupRule,
} from '@/modules/menu-item-option-groups/entities/menu-item-option-group.entity';
import { MenuItem } from '@/modules/menu-items/entities/menu-item.entity';
import { MenuItemsService } from '@/modules/menu-items/menu-items.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { AddToCartDto, CartItemGroupOptionDto } from './dto/add-to-cart.dto';
import { CartQueryDto } from './dto/cart-query.dto';
import { UpdateCartItemAmountDto } from './dto/update-cart-item-amount.dto';
import { CartItemOption } from './entities/cart-item-option.entity';
import { CartItem } from './entities/cart-item.entity';
import { Cart } from './entities/cart.entity';

@Injectable()
export class CartsService {
  constructor(
    @InjectRepository(Cart)
    private readonly cartRepository: Repository<Cart>,
    @InjectRepository(CartItem)
    private readonly cartItemRepository: Repository<CartItem>,
    @InjectRepository(CartItemOption)
    private readonly cartItemOptionRepository: Repository<CartItemOption>,

    private readonly menuItemsService: MenuItemsService,
    private readonly dataSource: DataSource,
  ) {}

  public validateMenuItemBelongsToRestaurant(menuItem: MenuItem, restaurantId: string): void {
    const belongsToRestaurant = menuItem.menuSections?.some((section) =>
      section.menus?.some((menu) => menu.restaurantId === restaurantId),
    );

    if (!belongsToRestaurant) {
      throw new BadRequestException('Menu item does not belong to the specified restaurant');
    }
  }

  public validateMenuItemAvailability(menuItem: MenuItem): void {
    const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

    const isAvailable = menuItem.menuSections?.some((section) => {
      if (
        !section.availableSchedule ||
        !Array.isArray(section.availableSchedule) ||
        section.availableSchedule.length === 0
      ) {
        return false;
      }

      return section.availableSchedule.some((schedule) => {
        if (schedule.isAllDay) {
          return schedule.day === currentDayOfWeek;
        }

        if (!schedule.start || !schedule.end) {
          return false;
        }

        return currentTime >= schedule.start && currentTime <= schedule.end;
      });
    });

    if (!isAvailable) {
      throw new BadRequestException('Menu item is not available at this time');
    }
  }

  public validateMenuItemOptions(menuItem: MenuItem, groupOptions: CartItemGroupOptionDto[]): void {
    const optionGroups = menuItem.menuItemOptionGroups || [];

    for (const option of groupOptions) {
      const optionGroup = optionGroups.find((group) => group.id === option.optionGroupId);
      if (!optionGroup) {
        throw new BadRequestException(`Option group ${option.optionGroupId} is not valid for this menu item`);
      }

      // this.validateMaxAmountOfOption(optionGroup, option);
    }
    // this.validateMenuItemOptionGroup(menuItem, groupOptions);
  }

  validateMaxAmountOfOption(optionGroup: MenuItemOptionGroup, option: CartItemGroupOptionDto): void {
    const optionGroupMaxAmount = optionGroup.maxAmountOfOption;
    for (const optionWithAmountItemDto of option.options) {
      const itemOption = optionGroup.menuItemOptions?.find((o) => o.id === optionWithAmountItemDto.id);
      if (!itemOption) {
        throw new BadRequestException(`Option ${optionWithAmountItemDto.id} is not valid for this menu item`);
      }
      if (
        !isNil(optionWithAmountItemDto?.amount) &&
        !(optionWithAmountItemDto?.amount > 0 && optionWithAmountItemDto?.amount <= optionGroupMaxAmount)
      ) {
        throw new BadRequestException(
          `Option ${optionWithAmountItemDto.id} amount must be between 1 and ${optionGroupMaxAmount}`,
        );
      }
    }
  }

  validateMenuItemOptionGroup(menuItem: MenuItem, groupOptions: CartItemGroupOptionDto[]): void {
    const optionGroups = menuItem.menuItemOptionGroups || [];
    for (const group of optionGroups) {
      const selectedOptions = groupOptions.find((o) => o.optionGroupId === group.id);
      if (!selectedOptions) {
        if (
          group.rule === MenuItemOptionGroupRule.MANDATORY_FIXED ||
          group.rule === MenuItemOptionGroupRule.MANDATORY_RANGE
        ) {
          throw new BadRequestException(`Option group ${group.publishedName} (${group.id}) is mandatory`);
        }
        continue;
      }
      const selectedCount = selectedOptions.options.length;
      switch (group.rule) {
        case MenuItemOptionGroupRule.OPTION:
          // Allow selecting 0 to all options - no validation needed
          break;
        case MenuItemOptionGroupRule.OPTION_MAX:
          if (!isNil(group.maxAmount) && selectedCount > group.maxAmount) {
            throw new BadRequestException(
              `Option group ${group.publishedName} allows maximum ${group.maxAmount} selections`,
            );
          }
          break;
        case MenuItemOptionGroupRule.MANDATORY_FIXED:
          if (selectedCount !== group.fixedAmount) {
            throw new BadRequestException(
              `Option group ${group.publishedName} requires exactly ${group.fixedAmount} selections`,
            );
          }
          break;
        case MenuItemOptionGroupRule.MANDATORY_RANGE:
          if (
            group.fromAmount &&
            group.toAmount &&
            (selectedCount < group.fromAmount || selectedCount > group.toAmount)
          ) {
            throw new BadRequestException(
              `Option group ${group.publishedName} requires between ${group.fromAmount} and ${group.toAmount} selections`,
            );
          }
          break;
      }
    }
  }

  async findCart(cartId: string, userId: string): Promise<Cart> {
    const queryBuilder = await this.cartRepository.createQueryBuilder('cart');

    queryBuilder.where('cart.id = :cartId', { cartId });
    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    this.addRelationsCartQueryBuilder(queryBuilder);

    const cart = await queryBuilder.getOne();

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }
    this.getMenuSectionAndItemPrice(cart);
    return cart;
  }

  async findCartWithMenuItemOptions(cartId: string, userId: string): Promise<Cart> {
    const queryBuilder = await this.cartRepository.createQueryBuilder('cart');

    queryBuilder.where('cart.id = :cartId', { cartId });
    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    this.addRelationsCartQueryBuilder(queryBuilder);

    queryBuilder
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('brand.merchantAccount', 'merchantAccount')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule2')
      .leftJoinAndSelect('cartItems.menuSection', 'menuSection2', 'menuSection2.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuSection2.availableSchedule', 'availableSchedule3')
      .leftJoinAndSelect('menuItem.mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroups2')
      .leftJoinAndSelect('mappingMenuItemOptionGroups2.menuItemOptionGroup', 'menuItemOptionGroup2')
      .leftJoinAndSelect('menuItemOptionGroup2.mappingMenuItemOptions', 'mappingMenuItemOptions2')
      .leftJoinAndSelect(
        'mappingMenuItemOptions2.menuItemOption',
        'menuItemOption2',
        'menuItemOption2.activeAt IS NOT NULL',
      );

    const cart = await queryBuilder.getOne();

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }

    // Validate active status of all cart components
    this.validateCartComponentsActiveStatus(cart);

    this.getMenuSectionAndItemPrice(cart, true);
    return cart;
  }

  private validateCartComponentsActiveStatus(cart: Cart): void {
    // Validate restaurant is active
    if (!cart.restaurant?.activeAt) {
      throw new BadRequestException('Restaurant is not active');
    }

    // Validate brand is active
    if (!cart.restaurant?.brand?.activeAt) {
      throw new BadRequestException('Brand is not active');
    }

    // Validate merchant account is active
    if (!cart.restaurant?.brand?.merchantAccount?.activeAt) {
      throw new BadRequestException('Merchant account is not active');
    }
  }

  async findActiveCartByRestaurant(userId: string, restaurantId: string): Promise<Cart | undefined> {
    const queryBuilder = await this.cartRepository.createQueryBuilder('cart');

    queryBuilder.where('cart.restaurantId = :restaurantId', { restaurantId });
    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    this.addRelationsCartQueryBuilder(queryBuilder);

    const cart = await queryBuilder.getOne();
    if (!cart) {
      return undefined;
    }

    this.getMenuSectionAndItemPrice(cart);

    return cart;
  }

  async updateCartItemAmount(
    userId: string,
    cartItemId: string,
    updateCartItemAmountDto: UpdateCartItemAmountDto,
  ): Promise<Cart> {
    if (!Object.keys(updateCartItemAmountDto).length) {
      throw new BadRequestException('No fields to update');
    }

    const cartItem = await this.cartItemRepository.findOne({
      where: { id: cartItemId, cart: { userId, completedAt: IsNull() } },
      relations: ['cart'],
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    await this.dataSource.transaction(async (manager) => {
      await manager.update(CartItem, { id: cartItemId }, updateCartItemAmountDto);
      await manager.update(Cart, { id: cartItem.cartId }, { updatedAt: new Date() });

      // Bổ sung xử lý groupOptions
      if (updateCartItemAmountDto.groupOptions) {
        // Lấy toàn bộ option hiện tại
        const currentOptions = await manager.find(CartItemOption, { where: { cartItemId } });
        // Tạo map cho việc so sánh
        const currentMap = new Map<string, CartItemOption>();
        for (const opt of currentOptions) {
          currentMap.set(opt.menuItemOptionGroupId + '-' + opt.menuItemOptionId, opt);
        }
        // Tạo set các key mới
        const newKeys = new Set<string>();
        for (const group of updateCartItemAmountDto.groupOptions) {
          for (const opt of group.options) {
            const key = group.optionGroupId + '-' + opt.id;
            newKeys.add(key);
            if (currentMap.has(key)) {
              // Nếu đã có, update amount nếu khác
              const exist = currentMap.get(key)!;
              if (exist.amount !== (opt.amount || 1)) {
                await manager.update(CartItemOption, { id: exist.id }, { amount: opt.amount || 1 });
              }
            } else {
              // Nếu chưa có, thêm mới
              const newOption = manager.create(CartItemOption, {
                cartItemId,
                menuItemOptionGroupId: group.optionGroupId,
                menuItemOptionId: opt.id,
                amount: opt.amount || 1,
              });
              await manager.save(newOption);
            }
          }
        }
        // Xóa các option không còn trong groupOptions mới
        for (const [key, exist] of currentMap.entries()) {
          if (!newKeys.has(key)) {
            await manager.softDelete(CartItemOption, { id: exist.id });
          }
        }
      }
    });

    return this.findCart(cartItem.cartId, userId);
  }

  async removeCartItem(userId: string, cartItemId: string): Promise<Cart> {
    const cartItem = await this.cartItemRepository.findOne({
      where: { id: cartItemId, cart: { userId, completedAt: IsNull() } },
      relations: ['cart'],
    });

    if (!cartItem) {
      throw new NotFoundException('Cart item not found');
    }

    await this.dataSource.transaction(async (manager) => {
      await manager.softRemove(cartItem);
      await manager.softDelete(CartItemOption, { cartItemId: cartItem.id });
      await manager.update(Cart, { id: cartItem.cartId }, { updatedAt: new Date() });
    });
    return this.findCart(cartItem.cartId, userId);
  }

  async deleteCart(userId: string, cartId: string) {
    const cart = await this.cartRepository.findOne({
      where: { id: cartId, userId, completedAt: IsNull() },
      relations: ['cartItems', 'cartItems.cartItemOptions'],
    });

    if (!cart) {
      throw new NotFoundException('Cart not found');
    }
    await this.cartRepository.softRemove(cart);

    return true;
  }

  async addToCart(userId: string, addToCartDto: AddToCartDto): Promise<Cart> {
    const { restaurantId, menuItemId, menuSectionId, amount, groupOptions, note } = addToCartDto;

    const menuItem = await this.menuItemsService.findOneByCartWithRelations(restaurantId, menuSectionId, menuItemId);

    // Validate menu item belongs to restaurant
    this.validateMenuItemBelongsToRestaurant(menuItem, restaurantId);

    // Validate menu item availability
    this.validateMenuItemAvailability(menuItem);

    // Validate menu item options
    this.validateMenuItemOptions(menuItem, groupOptions);

    return this.dataSource.transaction(async (manager) => {
      let cart = await manager.findOne(Cart, {
        where: { userId, restaurantId, completedAt: IsNull() },
      });

      if (!cart) {
        cart = manager.create(Cart, { userId, restaurantId } as Cart);
        cart = await manager.save(cart);
      }

      const cartItem = manager.create(CartItem, {
        cartId: cart.id,
        menuItemId,
        menuSectionId,
        amount,
        note,
      } as CartItem);

      const savedCartItem = await manager.save(cartItem);

      const cartItemOptions: CartItemOption[] = [];

      for (const option of groupOptions) {
        const optionAmountMap = new Map<string, number>();

        if (option.options && option.options.length > 0) {
          for (const optWithAmount of option.options) {
            optionAmountMap.set(optWithAmount.id, optWithAmount.amount || 1);
          }
        }

        for (const optionId of option.options.map((opt) => opt.id)) {
          const amount = optionAmountMap.has(optionId) ? optionAmountMap.get(optionId) : 1;

          const cartItemOption = manager.create(CartItemOption, {
            cartItemId: savedCartItem.id,
            menuItemOptionGroupId: option.optionGroupId,
            menuItemOptionId: optionId,
            amount: amount,
          } as CartItemOption);

          cartItemOptions.push(cartItemOption);
        }
      }

      if (cartItemOptions.length > 0) {
        await manager.save(cartItemOptions);
      }

      await manager.update(Cart, { id: cart.id }, { updatedAt: new Date() });

      const cartResult = await manager.findOne(Cart, {
        where: { id: cart.id },
        relations: ['cartItems', 'cartItems.cartItemOptions'],
      });

      if (!cartResult) {
        throw new NotFoundException('Cart not found');
      }

      return cartResult;
    });
  }

  async getYouMightAlsoLike(userId: string, restaurantId: string): Promise<MenuItem[]> {
    const existingCart = await this.cartRepository.findOne({
      where: { userId, restaurantId, completedAt: IsNull() },
      relations: ['cartItems'],
    });
    const menuItemsIds: string[] = Array.from(
      new Set(existingCart ? existingCart.cartItems.map((item) => item.menuItemId) : []),
    );

    const allEligibleItems = await this.menuItemsService.getListByRestaurantIdExcludingCartItemsWithValidation(
      restaurantId,
      menuItemsIds,
    );

    const shuffled = allEligibleItems.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, 10);
  }

  async findAll(userId: string, query: CartQueryDto): Promise<Pagination<Cart>> {
    const { page, limit, restaurantId } = query;

    const queryBuilder = this.cartRepository.createQueryBuilder('cart');

    if (userId) {
      queryBuilder.andWhere('cart.userId = :userId', { userId });
    }

    this.addRelationsCartQueryBuilder(queryBuilder);

    if (restaurantId) {
      queryBuilder.andWhere('cart.restaurantId = :restaurantId', { restaurantId });
    }

    const options: IPaginationOptions = { page, limit };
    const carts = await paginate<Cart>(queryBuilder, options);

    for (const cart of carts.items) {
      this.getMenuSectionAndItemPrice(cart);
    }

    return carts;
  }

  private addRelationsCartQueryBuilder(queryBuilder: SelectQueryBuilder<Cart>) {
    queryBuilder
      .leftJoinAndSelect('cart.restaurant', 'restaurant')
      .leftJoinAndSelect('cart.cartItems', 'cartItems')
      .leftJoinAndSelect('cartItems.menuItem', 'menuItem', 'menuItem.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'menuItem.mappingMenuSections',
        'mappingMenuSections',
        'mappingMenuSections.menuSectionId = cartItems.menuSectionId',
      )
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection', 'menuSection.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuSection.mappingMenus', 'mappingMenus')
      .leftJoinAndSelect('mappingMenus.menu', 'menu', 'menu.activeAt IS NOT NULL')
      .leftJoinAndSelect('cartItems.cartItemOptions', 'cartItemOptions')
      .leftJoinAndSelect('cartItemOptions.menuItemOption', 'menuItemOption', 'menuItemOption.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'menuItemOption.mappingMenuItemOptionGroups',
        'mappingMenuItemOptionGroups',
        'mappingMenuItemOptionGroups.menuItemOptionId = cartItemOptions.menuItemOptionId',
      )
      .leftJoinAndSelect('mappingMenuItemOptionGroups.menuItemOptionGroup', 'menuItemOptionGroup')
      .andWhere('cart.completedAt IS NULL')
      .orderBy('cart.updatedAt', 'DESC');
  }

  private getMenuSectionAndItemPrice(cart: Cart, validateExist: boolean = false) {
    for (const cartItem of cart.cartItems) {
      if (!cartItem.menuItem) {
        if (validateExist) throw new NotFoundException('Have menu item not found');
        continue;
      }

      if (validateExist && !cartItem.menuItem.activeAt) {
        if (validateExist) throw new NotFoundException('Have menu item not active');
        continue;
      }

      const menuSection = cartItem.menuItem.menuSections?.find((section) => section.id === cartItem.menuSectionId);
      if (validateExist && (!menuSection || menuSection.menus?.length === 0))
        throw new NotFoundException('Have menu section not found');
      cartItem.menuItem.price = menuSection?.itemPrice;
      cartItem.menuSection = menuSection;
      for (const cartItemOption of cartItem.cartItemOptions) {
        if (!cartItemOption.menuItemOption) {
          if (validateExist) throw new NotFoundException('Have menu item option not found');
          continue;
        }
        const menuItemOption = cartItemOption.menuItemOption;
        const menuItemOptionGroup = menuItemOption.menuItemOptionGroups?.find(
          (m) => m.id === cartItemOption.menuItemOptionGroupId,
        );
        if (!menuItemOptionGroup && validateExist) throw new NotFoundException('Have menu item option group not found');
        menuItemOption.price = menuItemOptionGroup?.optionPrice;
        cartItemOption.menuItemOptionGroup = menuItemOptionGroup;
      }
    }
  }
}
