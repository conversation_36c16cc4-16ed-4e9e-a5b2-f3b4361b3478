import { Pagination } from 'nestjs-typeorm-paginate';

import { UserMerchantId } from '@/common/decorators/user.decorator';
import { CheckNameExistsDto, NameExistsResponseDto } from '@/common/dtos/check-name-exists.dto';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Delete, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import { CreateMenuSectionDto } from './dtos/create-menu-section.dto';
import { ListMenuSectionDto } from './dtos/list-menu-section.dto';
import { UpdateMenuSectionDto } from './dtos/update-menu-section.dto';
import { MenuSection } from './entities/menu-section.entity';
import { MenuSectionsService } from './menu-sections.service';

@ApiTags('Menu Sections')
@Controller('menu-sections')
@Roles({ userType: UserType.MERCHANT_USER, role: '*' })
export class MenuSectionsController {
  constructor(private readonly menuSectionsService: MenuSectionsService) {}

  @Post('check-name-exists')
  @ApiOperation({ summary: 'Check if menu section name exists for restaurant' })
  async checkExists(@Body() dto: CheckNameExistsDto): Promise<NameExistsResponseDto> {
    const exists = await this.menuSectionsService.checkNameExists(
      dto.restaurantId,
      dto.internalName,
      dto.publishedName,
      dto.excludeId,
    );
    return { exists };
  }

  @Post()
  create(@Body() createMenuSectionDto: CreateMenuSectionDto): Promise<MenuSection> {
    return this.menuSectionsService.create(createMenuSectionDto, null);
  }

  @Get()
  findAll(
    @Query() listMenuSectionDto: ListMenuSectionDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<Pagination<MenuSection>> {
    return this.menuSectionsService.findAll(listMenuSectionDto, ownerId);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<MenuSection> {
    return this.menuSectionsService.findOne(id, ownerId);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMenuSectionDto: UpdateMenuSectionDto,
    @UserMerchantId() ownerId: string | null,
  ): Promise<MenuSection> {
    return this.menuSectionsService.update(id, updateMenuSectionDto, ownerId);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a menu section and all its relations' })
  delete(@Param('id', ParseUUIDPipe) id: string, @UserMerchantId() ownerId: string | null): Promise<MenuSection> {
    return this.menuSectionsService.delete(id, ownerId);
  }
}
